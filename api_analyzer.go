package newapi

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"log"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
)

// APIAnalyzer API定义分析器
type APIAnalyzer struct {
	FileSet      *token.FileSet
	Files        map[string]*ast.File
	APIBuilder   *APIBuilder
	TypeRegistry map[string]*TypeDefinition
	CurrentAPI   *APIDefinition
	// 新增字段：目录和模块分析
	Modules       map[string]*ModuleDefinition `json:"modules"`        // 模块信息
	RootPath      string                       `json:"root_path"`      // 根目录路径
	CurrentModule string                       `json:"current_module"` // 当前正在处理的模块
}

// APIDefinition 完整的API定义
type APIDefinition struct {
	Info        *APIInfoDefinition
	Groups      []*GroupDefinition
	Endpoints   []*EndpointDefinition
	Types       []*TypeDefinition
	Middlewares []*MiddlewareDefinition
	Security    []*SecurityDefinition
}

// APIInfoDefinition API信息定义
type APIInfoDefinition struct {
	Title       string
	Description string
	Version     string
	Author      string
	Email       string
	Host        string
	BasePath    string
}

// SecurityDefinition 安全定义
type SecurityDefinition2 struct {
	Name        string
	Type        string
	Description string
	In          string
	Header      string
}

// APIBuilder API构建器分析
type APIBuilder struct {
	CurrentGroup    *GroupDefinition
	CurrentEndpoint *EndpointDefinition
	BuilderChain    []string
}

// NewAPIAnalyzer 创建新的API分析器
func NewAPIAnalyzer() *APIAnalyzer {
	return &APIAnalyzer{
		FileSet:      token.NewFileSet(),
		Files:        make(map[string]*ast.File),
		APIBuilder:   &APIBuilder{},
		TypeRegistry: make(map[string]*TypeDefinition),
		Modules:      make(map[string]*ModuleDefinition),
		CurrentAPI: &APIDefinition{
			Groups:      make([]*GroupDefinition, 0),
			Endpoints:   make([]*EndpointDefinition, 0),
			Types:       make([]*TypeDefinition, 0),
			Middlewares: make([]*MiddlewareDefinition, 0),
			Security:    make([]*SecurityDefinition, 0),
		},
	}
}

// AnalyzeFile 分析单个Go文件中的API定义
func (a *APIAnalyzer) AnalyzeFile(filename string) error {
	file, err := parser.ParseFile(a.FileSet, filename, nil, parser.ParseComments)
	if err != nil {
		return fmt.Errorf("解析文件 %s 失败: %v", filename, err)
	}

	a.Files[filename] = file
	return a.analyzeASTFile(file)
}

// analyzeASTFile 分析AST文件
func (a *APIAnalyzer) analyzeASTFile(file *ast.File) error {
	// 首先分析所有类型定义
	for _, decl := range file.Decls {
		if genDecl, ok := decl.(*ast.GenDecl); ok && genDecl.Tok == token.TYPE {
			if err := a.analyzeTypeDeclarations(genDecl); err != nil {
				return err
			}
		}
	}

	// 然后分析函数定义，查找API构建逻辑
	for _, decl := range file.Decls {
		if funcDecl, ok := decl.(*ast.FuncDecl); ok {
			if err := a.analyzeFunctionForAPI(funcDecl); err != nil {
				return err
			}
		}
	}

	// 最后分析变量定义，查找API实例
	for _, decl := range file.Decls {
		if genDecl, ok := decl.(*ast.GenDecl); ok && genDecl.Tok == token.VAR {
			if err := a.analyzeVariableDeclarations(genDecl); err != nil {
				return err
			}
		}
	}

	return nil
}

// analyzeTypeDeclarations 分析类型声明
func (a *APIAnalyzer) analyzeTypeDeclarations(decl *ast.GenDecl) error {
	for _, spec := range decl.Specs {
		if typeSpec, ok := spec.(*ast.TypeSpec); ok {
			typeDef := &TypeDefinition{
				Name:    typeSpec.Name.Name,
				Fields:  make([]*FieldDefinition, 0),
				Methods: make([]*MethodDefinition, 0),
				Tags:    make(map[string]string),
			}

			if decl.Doc != nil {
				typeDef.Comment = decl.Doc.Text()
			}

			// 分析结构体类型
			if structType, ok := typeSpec.Type.(*ast.StructType); ok {
				typeDef.IsStruct = true
				if err := a.analyzeStructFields(structType, typeDef); err != nil {
					return err
				}
			}

			// 注册类型
			a.TypeRegistry[typeDef.Name] = typeDef
			a.CurrentAPI.Types = append(a.CurrentAPI.Types, typeDef)

			log.Printf("注册类型: %s (字段数: %d)", typeDef.Name, len(typeDef.Fields))
		}
	}
	return nil
}

// analyzeStructFields 分析结构体字段
func (a *APIAnalyzer) analyzeStructFields(structType *ast.StructType, typeDef *TypeDefinition) error {
	for _, field := range structType.Fields.List {
		fieldDef := &FieldDefinition{}

		// 字段名
		if len(field.Names) > 0 {
			fieldDef.Name = field.Names[0].Name
		} else {
			// 匿名字段（嵌入）
			fieldDef.Name = a.getTypeString(field.Type)
		}

		// 字段类型
		fieldDef.Type = a.getTypeString(field.Type)

		// 解析字段标签
		if field.Tag != nil {
			tagValue := strings.Trim(field.Tag.Value, "`")
			a.parseStructTags(tagValue, fieldDef)
		}

		// 字段注释
		if field.Doc != nil {
			fieldDef.Comment = strings.TrimSpace(field.Doc.Text())
		} else if field.Comment != nil {
			fieldDef.Comment = strings.TrimSpace(field.Comment.Text())
		}

		typeDef.Fields = append(typeDef.Fields, fieldDef)
	}
	return nil
}

// analyzeFunctionForAPI 分析函数中的API定义
func (a *APIAnalyzer) analyzeFunctionForAPI(funcDecl *ast.FuncDecl) error {
	funcName := funcDecl.Name.Name

	// 检查是否是API定义函数
	if a.isAPIDefinitionFunction(funcName) {
		log.Printf("分析API定义函数: %s", funcName)

		if funcDecl.Body != nil {
			return a.analyzeFunctionBody(funcDecl.Body)
		}
	}

	return nil
}

// isAPIDefinitionFunction 检查是否是API定义函数
func (a *APIAnalyzer) isAPIDefinitionFunction(funcName string) bool {
	lowerName := strings.ToLower(funcName)
	return strings.Contains(lowerName, "api") ||
		strings.Contains(lowerName, "define") ||
		strings.Contains(lowerName, "setup") ||
		strings.Contains(lowerName, "route") ||
		strings.HasPrefix(lowerName, "create")
}

// analyzeFunctionBody 分析函数体
func (a *APIAnalyzer) analyzeFunctionBody(body *ast.BlockStmt) error {
	for _, stmt := range body.List {
		if err := a.analyzeStatement(stmt); err != nil {
			return err
		}
	}
	return nil
}

// analyzeStatement 分析语句
func (a *APIAnalyzer) analyzeStatement(stmt ast.Stmt) error {
	switch s := stmt.(type) {
	case *ast.AssignStmt:
		return a.analyzeAssignment(s)
	case *ast.ExprStmt:
		return a.analyzeExpression(s.X)
	case *ast.DeclStmt:
		return a.analyzeDeclaration(s.Decl)
	}
	return nil
}

// analyzeAssignment 分析赋值语句
func (a *APIAnalyzer) analyzeAssignment(stmt *ast.AssignStmt) error {
	for i, rhs := range stmt.Rhs {
		if i < len(stmt.Lhs) {
			var varName string
			if ident, ok := stmt.Lhs[i].(*ast.Ident); ok {
				varName = ident.Name
			}

			if callExpr, ok := rhs.(*ast.CallExpr); ok {
				if err := a.analyzeAPICall(callExpr, varName); err != nil {
					return err
				}
			}
		}
	}
	return nil
}

// analyzeExpression 分析表达式
func (a *APIAnalyzer) analyzeExpression(expr ast.Expr) error {
	if callExpr, ok := expr.(*ast.CallExpr); ok {
		return a.analyzeAPICall(callExpr, "")
	}
	return nil
}

// analyzeAPICall 分析API调用
func (a *APIAnalyzer) analyzeAPICall(expr *ast.CallExpr, varName string) error {
	funcName := a.getFunctionName(expr.Fun)

	switch funcName {
	case "NewAPI":
		return a.parseNewAPICall(expr, varName)
	case "NewGroup":
		return a.parseNewGroupCall(expr, varName)
	case "AddGroup", "AddEndpoint", "AddSecurity", "AddMiddleware":
		return a.parseAPIMethodCall(expr, funcName)
	}

	// 处理链式调用
	if selectorExpr, ok := expr.Fun.(*ast.SelectorExpr); ok {
		methodName := selectorExpr.Sel.Name

		switch methodName {
		case "GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS":
			return a.parseHTTPMethodCall(expr, methodName)
		case "Handler", "Summary", "Description", "Tags", "Request", "Response", "Security", "Middleware":
			return a.parseEndpointBuilderCall(expr, methodName)
		case "WithTags", "WithMiddleware":
			return a.parseGroupBuilderCall(expr, methodName)
		case "Build":
			return a.parseBuildCall(expr)
		}
	}

	return nil
}

// parseNewAPICall 解析NewAPI调用
func (a *APIAnalyzer) parseNewAPICall(expr *ast.CallExpr, varName string) error {
	if len(expr.Args) > 0 {
		// 解析APIInfo参数
		if compositeLit, ok := expr.Args[0].(*ast.CompositeLit); ok {
			apiInfo := &APIInfoDefinition{}
			if err := a.parseAPIInfoComposite(compositeLit, apiInfo); err != nil {
				return err
			}
			a.CurrentAPI.Info = apiInfo
			log.Printf("解析到API信息: %s v%s", apiInfo.Title, apiInfo.Version)
		}
	}
	return nil
}

// parseAPIInfoComposite 解析APIInfo复合字面量
func (a *APIAnalyzer) parseAPIInfoComposite(lit *ast.CompositeLit, apiInfo *APIInfoDefinition) error {
	for _, elt := range lit.Elts {
		if kv, ok := elt.(*ast.KeyValueExpr); ok {
			key := ""
			if ident, ok := kv.Key.(*ast.Ident); ok {
				key = ident.Name
			}

			value := ""
			if basicLit, ok := kv.Value.(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
				value, _ = strconv.Unquote(basicLit.Value)
			}

			switch key {
			case "Title":
				apiInfo.Title = value
			case "Description":
				apiInfo.Description = value
			case "Version":
				apiInfo.Version = value
			case "Author":
				apiInfo.Author = value
			case "Email":
				apiInfo.Email = value
			case "Host":
				apiInfo.Host = value
			case "BasePath":
				apiInfo.BasePath = value
			}
		}
	}
	return nil
}

// parseAPIMethodCall 解析API方法调用 (AddGroup, AddEndpoint, AddSecurity, AddMiddleware)
func (a *APIAnalyzer) parseAPIMethodCall(expr *ast.CallExpr, methodName string) error {
	switch methodName {
	case "AddGroup":
		return a.parseAddGroupCall(expr)
	case "AddEndpoint":
		return a.parseAddEndpointCall(expr)
	case "AddSecurity":
		return a.parseAddSecurityCall(expr)
	case "AddMiddleware":
		return a.parseAddMiddlewareCall(expr)
	}
	return nil
}

// parseAddGroupCall 解析AddGroup调用
func (a *APIAnalyzer) parseAddGroupCall(expr *ast.CallExpr) error {
	// AddGroup通常接受一个Group类型的参数
	if len(expr.Args) > 0 {
		// 这里可以解析Group参数，但由于Group通常是通过变量传递的
		// 我们主要依赖于NewGroup的解析
		log.Printf("解析到AddGroup调用")
	}
	return nil
}

// parseAddEndpointCall 解析AddEndpoint调用
func (a *APIAnalyzer) parseAddEndpointCall(expr *ast.CallExpr) error {
	// AddEndpoint通常接受一个Endpoint类型的参数
	if len(expr.Args) > 0 {
		log.Printf("解析到AddEndpoint调用")
		// 这里可以解析Endpoint结构体字面量
		if compositeLit, ok := expr.Args[0].(*ast.CompositeLit); ok {
			return a.parseEndpointComposite(compositeLit)
		}
	}
	return nil
}

// parseAddSecurityCall 解析AddSecurity调用
func (a *APIAnalyzer) parseAddSecurityCall(expr *ast.CallExpr) error {
	// AddSecurity通常接受name和SecurityDefinition参数
	if len(expr.Args) >= 2 {
		var securityName string
		if basicLit, ok := expr.Args[0].(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
			securityName, _ = strconv.Unquote(basicLit.Value)
		}

		if compositeLit, ok := expr.Args[1].(*ast.CompositeLit); ok {
			securityDef := &SecurityDefinition{
				Name: securityName,
			}
			if err := a.parseSecurityComposite(compositeLit, securityDef); err != nil {
				return err
			}
			a.CurrentAPI.Security = append(a.CurrentAPI.Security, securityDef)
			log.Printf("解析到安全定义: %s", securityName)
		}
	}
	return nil
}

// parseAddMiddlewareCall 解析AddMiddleware调用
func (a *APIAnalyzer) parseAddMiddlewareCall(expr *ast.CallExpr) error {
	// AddMiddleware通常接受name和middleware函数参数
	if len(expr.Args) >= 1 {
		var middlewareName string
		if basicLit, ok := expr.Args[0].(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
			middlewareName, _ = strconv.Unquote(basicLit.Value)
		}

		middlewareDef := &MiddlewareDefinition{
			Name: middlewareName,
			Type: "custom",
		}
		a.CurrentAPI.Middlewares = append(a.CurrentAPI.Middlewares, middlewareDef)
		log.Printf("解析到中间件定义: %s", middlewareName)
	}
	return nil
}

// parseEndpointComposite 解析Endpoint复合字面量
func (a *APIAnalyzer) parseEndpointComposite(lit *ast.CompositeLit) error {
	endpointDef := &EndpointDefinition{
		Tags:        make([]string, 0),
		Middlewares: make([]string, 0),
		Security:    make([]string, 0),
	}

	for _, elt := range lit.Elts {
		if kv, ok := elt.(*ast.KeyValueExpr); ok {
			key := ""
			if ident, ok := kv.Key.(*ast.Ident); ok {
				key = ident.Name
			}

			switch key {
			case "Method":
				if selectorExpr, ok := kv.Value.(*ast.SelectorExpr); ok {
					endpointDef.Method = selectorExpr.Sel.Name
				}
			case "Path":
				if basicLit, ok := kv.Value.(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
					path, _ := strconv.Unquote(basicLit.Value)
					endpointDef.Path = path
				}
			case "Handler":
				if basicLit, ok := kv.Value.(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
					handler, _ := strconv.Unquote(basicLit.Value)
					endpointDef.Handler = handler
				}
			case "Summary":
				if basicLit, ok := kv.Value.(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
					summary, _ := strconv.Unquote(basicLit.Value)
					endpointDef.Summary = summary
				}
			case "Description":
				if basicLit, ok := kv.Value.(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
					description, _ := strconv.Unquote(basicLit.Value)
					endpointDef.Description = description
				}
			case "RequestType":
				typeStr := a.getTypeString(kv.Value)
				if typeDef, exists := a.TypeRegistry[typeStr]; exists {
					endpointDef.RequestType = typeDef
				}
			case "ResponseType":
				typeStr := a.getTypeString(kv.Value)
				if typeDef, exists := a.TypeRegistry[typeStr]; exists {
					endpointDef.ResponseType = typeDef
				}
			}
		}
	}

	a.CurrentAPI.Endpoints = append(a.CurrentAPI.Endpoints, endpointDef)
	log.Printf("解析到独立端点: %s %s", endpointDef.Method, endpointDef.Path)
	return nil
}

// parseSecurityComposite 解析SecurityDefinition复合字面量
func (a *APIAnalyzer) parseSecurityComposite(lit *ast.CompositeLit, securityDef *SecurityDefinition) error {
	for _, elt := range lit.Elts {
		if kv, ok := elt.(*ast.KeyValueExpr); ok {
			key := ""
			if ident, ok := kv.Key.(*ast.Ident); ok {
				key = ident.Name
			}

			value := ""
			if basicLit, ok := kv.Value.(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
				value, _ = strconv.Unquote(basicLit.Value)
			}

			switch key {
			case "Type":
				securityDef.Type = value
			case "Description":
				securityDef.Description = value
			case "In":
				securityDef.In = value
			case "Name":
				securityDef.Name = value
			}
		}
	}
	return nil
}

// parseNewGroupCall 解析NewGroup调用
func (a *APIAnalyzer) parseNewGroupCall(expr *ast.CallExpr, varName string) error {
	if len(expr.Args) > 0 {
		if basicLit, ok := expr.Args[0].(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
			prefix, _ := strconv.Unquote(basicLit.Value)

			groupDef := &GroupDefinition{
				Name:      varName,
				Prefix:    prefix,
				Tags:      make([]string, 0),
				Endpoints: make([]*EndpointDefinition, 0),
			}

			a.APIBuilder.CurrentGroup = groupDef
			a.CurrentAPI.Groups = append(a.CurrentAPI.Groups, groupDef)

			log.Printf("解析到路由组: %s -> %s", varName, prefix)
		}
	}
	return nil
}

// parseHTTPMethodCall 解析HTTP方法调用
func (a *APIAnalyzer) parseHTTPMethodCall(expr *ast.CallExpr, method string) error {
	if len(expr.Args) > 0 {
		if basicLit, ok := expr.Args[0].(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
			path, _ := strconv.Unquote(basicLit.Value)

			endpointDef := &EndpointDefinition{
				Method:      method,
				Path:        path,
				Tags:        make([]string, 0),
				Middlewares: make([]string, 0),
				Security:    make([]string, 0),
			}

			a.APIBuilder.CurrentEndpoint = endpointDef

			// 如果当前在路由组中，则添加到组，否则添加到全局端点
			if a.APIBuilder.CurrentGroup != nil {
				a.APIBuilder.CurrentGroup.Endpoints = append(a.APIBuilder.CurrentGroup.Endpoints, endpointDef)
			} else {
				a.CurrentAPI.Endpoints = append(a.CurrentAPI.Endpoints, endpointDef)
			}

			log.Printf("解析到端点: %s %s", method, path)
		}
	}
	return nil
}

// parseEndpointBuilderCall 解析端点构建器调用
func (a *APIAnalyzer) parseEndpointBuilderCall(expr *ast.CallExpr, methodName string) error {
	if a.APIBuilder.CurrentEndpoint == nil {
		return nil
	}

	endpoint := a.APIBuilder.CurrentEndpoint

	if len(expr.Args) > 0 {
		switch methodName {
		case "Handler":
			if basicLit, ok := expr.Args[0].(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
				handler, _ := strconv.Unquote(basicLit.Value)
				endpoint.Handler = handler
			}
		case "Summary":
			if basicLit, ok := expr.Args[0].(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
				summary, _ := strconv.Unquote(basicLit.Value)
				endpoint.Summary = summary
			}
		case "Description":
			if basicLit, ok := expr.Args[0].(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
				description, _ := strconv.Unquote(basicLit.Value)
				endpoint.Description = description
			}
		case "Request":
			if err := a.parseRequestType(expr.Args[0], endpoint); err != nil {
				return err
			}
		case "Response":
			if err := a.parseResponseType(expr.Args[0], endpoint); err != nil {
				return err
			}
		case "Tags":
			if err := a.parseStringSliceArgs(expr.Args, &endpoint.Tags); err != nil {
				return err
			}
		case "Security":
			if err := a.parseStringSliceArgs(expr.Args, &endpoint.Security); err != nil {
				return err
			}
		}
	}

	return nil
}

// parseRequestType 解析请求类型
func (a *APIAnalyzer) parseRequestType(expr ast.Expr, endpoint *EndpointDefinition) error {
	typeStr := a.getTypeString(expr)

	// 去掉复合字面量的{}
	if strings.HasSuffix(typeStr, "{}") {
		typeStr = strings.TrimSuffix(typeStr, "{}")
	}

	if typeDef, exists := a.TypeRegistry[typeStr]; exists {
		endpoint.RequestType = typeDef
		log.Printf("端点 %s %s 的请求类型: %s", endpoint.Method, endpoint.Path, typeStr)
	}

	return nil
}

// parseResponseType 解析响应类型
func (a *APIAnalyzer) parseResponseType(expr ast.Expr, endpoint *EndpointDefinition) error {
	typeStr := a.getTypeString(expr)

	// 去掉复合字面量的{}
	if strings.HasSuffix(typeStr, "{}") {
		typeStr = strings.TrimSuffix(typeStr, "{}")
	}

	if typeDef, exists := a.TypeRegistry[typeStr]; exists {
		endpoint.ResponseType = typeDef
		log.Printf("端点 %s %s 的响应类型: %s", endpoint.Method, endpoint.Path, typeStr)
	}

	return nil
}

// parseStringSliceArgs 解析字符串切片参数
func (a *APIAnalyzer) parseStringSliceArgs(args []ast.Expr, target *[]string) error {
	for _, arg := range args {
		if basicLit, ok := arg.(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
			value, _ := strconv.Unquote(basicLit.Value)
			*target = append(*target, value)
		}
	}
	return nil
}

// parseGroupBuilderCall 解析路由组构建器调用
func (a *APIAnalyzer) parseGroupBuilderCall(expr *ast.CallExpr, methodName string) error {
	if a.APIBuilder.CurrentGroup == nil {
		return nil
	}

	group := a.APIBuilder.CurrentGroup

	switch methodName {
	case "WithTags":
		return a.parseStringSliceArgs(expr.Args, &group.Tags)
	case "WithMiddleware":
		return a.parseStringSliceArgs(expr.Args, &group.Middlewares)
	}

	return nil
}

// parseBuildCall 解析Build调用
func (a *APIAnalyzer) parseBuildCall(expr *ast.CallExpr) error {
	// Build调用表示当前构建器链结束
	// 这里可以进行一些清理工作
	return nil
}

// analyzeVariableDeclarations 分析变量声明
func (a *APIAnalyzer) analyzeVariableDeclarations(decl *ast.GenDecl) error {
	// 这里可以分析全局变量中的API定义
	return nil
}

// analyzeDeclaration 分析声明
func (a *APIAnalyzer) analyzeDeclaration(decl ast.Decl) error {
	// 处理局部声明
	return nil
}

// getTypeString 获取类型的字符串表示
func (a *APIAnalyzer) getTypeString(expr ast.Expr) string {
	switch t := expr.(type) {
	case *ast.Ident:
		return t.Name
	case *ast.SelectorExpr:
		return a.getTypeString(t.X) + "." + t.Sel.Name
	case *ast.StarExpr:
		return "*" + a.getTypeString(t.X)
	case *ast.ArrayType:
		return "[]" + a.getTypeString(t.Elt)
	case *ast.MapType:
		return "map[" + a.getTypeString(t.Key) + "]" + a.getTypeString(t.Value)
	case *ast.InterfaceType:
		return "interface{}"
	case *ast.CompositeLit:
		return a.getTypeString(t.Type)
	default:
		return "unknown"
	}
}

// getFunctionName 获取函数名
func (a *APIAnalyzer) getFunctionName(expr ast.Expr) string {
	switch e := expr.(type) {
	case *ast.Ident:
		return e.Name
	case *ast.SelectorExpr:
		return e.Sel.Name
	default:
		return ""
	}
}

// parseStructTags 解析结构体标签
func (a *APIAnalyzer) parseStructTags(tagStr string, field *FieldDefinition) {
	tag := reflect.StructTag(tagStr)

	if jsonTag := tag.Get("json"); jsonTag != "" {
		field.JSONTag = jsonTag
		if strings.Contains(jsonTag, "omitempty") {
			field.Optional = true
		}
	}

	if formTag := tag.Get("form"); formTag != "" {
		field.FormTag = formTag
	}

	if validateTag := tag.Get("validate"); validateTag != "" {
		field.ValidTag = validateTag
	}

	if uriTag := tag.Get("uri"); uriTag != "" {
		field.FormTag = uriTag // URI标签通常用于路径参数
	}

	if pathTag := tag.Get("path"); pathTag != "" {
		field.FormTag = pathTag // Path标签用于路径参数
	}
}

// GetAPIDefinition 获取解析后的API定义
func (a *APIAnalyzer) GetAPIDefinition() *APIDefinition {
	return a.CurrentAPI
}

// PrintAnalysisResults 打印分析结果
func (a *APIAnalyzer) PrintAnalysisResults() {
	fmt.Println("=== API定义分析结果 ===")

	if a.CurrentAPI.Info != nil {
		info := a.CurrentAPI.Info
		fmt.Printf("API信息:\n")
		fmt.Printf("  标题: %s\n", info.Title)
		fmt.Printf("  版本: %s\n", info.Version)
		fmt.Printf("  描述: %s\n", info.Description)
		fmt.Printf("  作者: %s\n", info.Author)
		fmt.Printf("  邮箱: %s\n", info.Email)
		fmt.Printf("\n")
	}

	fmt.Printf("类型定义数量: %d\n", len(a.CurrentAPI.Types))
	for _, typeDef := range a.CurrentAPI.Types {
		fmt.Printf("  类型: %s (字段数: %d)\n", typeDef.Name, len(typeDef.Fields))
	}
	fmt.Printf("\n")

	fmt.Printf("路由组数量: %d\n", len(a.CurrentAPI.Groups))
	for _, group := range a.CurrentAPI.Groups {
		fmt.Printf("  路由组: %s -> %s (端点数: %d)\n", group.Name, group.Prefix, len(group.Endpoints))
		for _, endpoint := range group.Endpoints {
			reqType := ""
			respType := ""
			if endpoint.RequestType != nil {
				reqType = fmt.Sprintf(" (%s)", endpoint.RequestType.Name)
			}
			if endpoint.ResponseType != nil {
				respType = fmt.Sprintf(" -> %s", endpoint.ResponseType.Name)
			}
			fmt.Printf("    %s %s%s -> %s%s%s\n",
				endpoint.Method, endpoint.Path, reqType, endpoint.Handler, respType,
				func() string {
					if endpoint.Summary != "" {
						return " // " + endpoint.Summary
					}
					return ""
				}(),
			)
		}
	}
	fmt.Printf("\n")

	fmt.Printf("独立端点数量: %d\n", len(a.CurrentAPI.Endpoints))
	for _, endpoint := range a.CurrentAPI.Endpoints {
		reqType := ""
		respType := ""
		if endpoint.RequestType != nil {
			reqType = fmt.Sprintf(" (%s)", endpoint.RequestType.Name)
		}
		if endpoint.ResponseType != nil {
			respType = fmt.Sprintf(" -> %s", endpoint.ResponseType.Name)
		}
		fmt.Printf("  %s %s%s -> %s%s%s\n",
			endpoint.Method, endpoint.Path, reqType, endpoint.Handler, respType,
			func() string {
				if endpoint.Summary != "" {
					return " // " + endpoint.Summary
				}
				return ""
			}())
	}

	// 打印模块信息
	fmt.Printf("\n模块数量: %d\n", len(a.Modules))
	for name, module := range a.Modules {
		fmt.Printf("  模块: %s -> %s (类型:%d, 组:%d, 端点:%d)\n",
			name, module.Path, len(module.Types), len(module.Groups), len(module.Endpoints))
	}
}

// AnalyzeDirectory 递归分析目录结构
func (a *APIAnalyzer) AnalyzeDirectory(rootDir string) error {
	a.RootPath = rootDir
	return a.analyzeDirectoryWithModule(rootDir, "")
}

// analyzeDirectoryWithModule 分析目录并识别模块
func (a *APIAnalyzer) analyzeDirectoryWithModule(dir string, parentModule string) error {
	// 计算相对路径和模块名
	relPath, err := filepath.Rel(a.RootPath, dir)
	if err != nil {
		return err
	}

	moduleName := a.inferModuleName(relPath)
	if parentModule != "" {
		moduleName = parentModule + "/" + moduleName
	}

	a.CurrentModule = moduleName

	// 创建模块定义
	module := &ModuleDefinition{
		Name:        filepath.Base(dir),
		Path:        dir,
		Description: fmt.Sprintf("功能模块: %s", moduleName),
		Package:     a.inferPackageName(dir),
		Types:       make([]*TypeDefinition, 0),
		Groups:      make([]*GroupDefinition, 0),
		Endpoints:   make([]*EndpointDefinition, 0),
		SubModules:  make([]*ModuleDefinition, 0),
		Files:       make([]string, 0),
		DependsOn:   make([]string, 0),
	}

	// 解析当前目录中的Go文件
	pkgs, err := parser.ParseDir(a.FileSet, dir, nil, parser.ParseComments)
	if err != nil {
		return fmt.Errorf("解析目录 %s 失败: %v", dir, err)
	}

	for pkgName, pkg := range pkgs {
		log.Printf("解析包: %s (模块: %s)", pkgName, moduleName)
		for filename, file := range pkg.Files {
			a.Files[filename] = file
			module.Files = append(module.Files, filename)

			// 设置文件信息到解析对象中
			if err := a.analyzeFileWithModule(file, filename, moduleName); err != nil {
				return fmt.Errorf("分析文件 %s 失败: %v", filename, err)
			}
		}
	}

	// 收集模块中的类型、组和端点
	a.collectModuleElements(module)

	// 存储模块
	a.Modules[moduleName] = module

	// 递归处理子目录
	return a.processSubDirectories(dir, moduleName)
}

// analyzeFileWithModule 分析文件并设置模块信息
func (a *APIAnalyzer) analyzeFileWithModule(file *ast.File, filename string, moduleName string) error {
	dir := filepath.Dir(filename)
	relPath, _ := filepath.Rel(a.RootPath, filename)

	// 记录分析前的状态
	typesCountBefore := len(a.CurrentAPI.Types)
	groupsCountBefore := len(a.CurrentAPI.Groups)
	endpointsCountBefore := len(a.CurrentAPI.Endpoints)

	// 先进行正常的文件分析
	if err := a.analyzeASTFile(file); err != nil {
		return err
	}

	// 为所有新解析的元素设置模块信息
	a.setModuleInfoForNewElements(filename, dir, moduleName, relPath,
		typesCountBefore, groupsCountBefore, endpointsCountBefore)

	return nil
}

// setModuleInfoForNewElements 为新解析的元素设置模块信息
func (a *APIAnalyzer) setModuleInfoForNewElements(filePath, directory, module, relativePath string,
	typesCountBefore, groupsCountBefore, endpointsCountBefore int) {

	// 为新的类型定义设置模块信息
	for i := typesCountBefore; i < len(a.CurrentAPI.Types); i++ {
		typeDef := a.CurrentAPI.Types[i]
		typeDef.FilePath = filePath
		typeDef.Directory = directory
		typeDef.Module = module
		typeDef.RelativePath = relativePath
	}

	// 为新的路由组设置模块信息
	for i := groupsCountBefore; i < len(a.CurrentAPI.Groups); i++ {
		group := a.CurrentAPI.Groups[i]
		group.FilePath = filePath
		group.Directory = directory
		group.Module = module
		group.RelativePath = relativePath
	}

	// 为新的端点设置模块信息
	for i := endpointsCountBefore; i < len(a.CurrentAPI.Endpoints); i++ {
		endpoint := a.CurrentAPI.Endpoints[i]
		endpoint.FilePath = filePath
		endpoint.Directory = directory
		endpoint.Module = module
		endpoint.RelativePath = relativePath
	}
}

// collectModuleElements 收集模块中的元素
func (a *APIAnalyzer) collectModuleElements(module *ModuleDefinition) {
	// 收集类型
	for _, typeDef := range a.CurrentAPI.Types {
		if typeDef.Module == module.Name || strings.HasPrefix(typeDef.Module, module.Name+"/") {
			module.Types = append(module.Types, typeDef)
		}
	}

	// 收集路由组
	for _, group := range a.CurrentAPI.Groups {
		if group.Module == module.Name || strings.HasPrefix(group.Module, module.Name+"/") {
			module.Groups = append(module.Groups, group)
		}
	}

	// 收集端点
	for _, endpoint := range a.CurrentAPI.Endpoints {
		if endpoint.Module == module.Name || strings.HasPrefix(endpoint.Module, module.Name+"/") {
			module.Endpoints = append(module.Endpoints, endpoint)
		}
	}
}

// processSubDirectories 处理子目录
func (a *APIAnalyzer) processSubDirectories(dir string, parentModule string) error {
	entries, err := os.ReadDir(dir)
	if err != nil {
		return err
	}

	for _, entry := range entries {
		if entry.IsDir() {
			// 跳过隐藏目录和特殊目录
			name := entry.Name()
			if strings.HasPrefix(name, ".") ||
				name == "vendor" ||
				name == "node_modules" {
				continue
			}

			subDir := filepath.Join(dir, name)
			// 递归处理子目录
			if err := a.analyzeDirectoryWithModule(subDir, parentModule); err != nil {
				log.Printf("警告: 处理子目录 %s 失败: %v", subDir, err)
			}
		}
	}

	return nil
}

// inferModuleName 推断模块名称
func (a *APIAnalyzer) inferModuleName(relPath string) string {
	if relPath == "." || relPath == "" {
		return "root"
	}
	return strings.ReplaceAll(relPath, string(filepath.Separator), "/")
}

// inferPackageName 推断包名
func (a *APIAnalyzer) inferPackageName(dir string) string {
	baseName := filepath.Base(dir)
	// 简单的包名推断逻辑
	if baseName == "." {
		return "main"
	}
	return baseName
}

// GetModuleByPath 根据路径获取模块
func (a *APIAnalyzer) GetModuleByPath(path string) *ModuleDefinition {
	for _, module := range a.Modules {
		if module.Path == path {
			return module
		}
	}
	return nil
}

// GetModulesByTag 根据标签获取模块
func (a *APIAnalyzer) GetModulesByTag(tag string) []*ModuleDefinition {
	var modules []*ModuleDefinition
	for _, module := range a.Modules {
		// 检查模块中的组或端点是否包含指定标签
		for _, group := range module.Groups {
			for _, groupTag := range group.Tags {
				if groupTag == tag {
					modules = append(modules, module)
					goto nextModule
				}
			}
		}
		for _, endpoint := range module.Endpoints {
			for _, endpointTag := range endpoint.Tags {
				if endpointTag == tag {
					modules = append(modules, module)
					goto nextModule
				}
			}
		}
	nextModule:
	}
	return modules
}

// GetModuleHierarchy 获取模块层次结构
func (a *APIAnalyzer) GetModuleHierarchy() map[string][]*ModuleDefinition {
	hierarchy := make(map[string][]*ModuleDefinition)

	for _, module := range a.Modules {
		// 计算模块层次
		parts := strings.Split(module.Name, "/")
		if len(parts) == 1 {
			// 顶级模块
			hierarchy["root"] = append(hierarchy["root"], module)
		} else {
			// 子模块
			parent := strings.Join(parts[:len(parts)-1], "/")
			hierarchy[parent] = append(hierarchy[parent], module)
		}
	}

	return hierarchy
}
