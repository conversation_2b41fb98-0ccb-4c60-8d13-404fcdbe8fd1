package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"admin-amis-zero/newapi"
	"demo/api" // 导入我们自己的API定义包
)

// main 函数解析api_example.go并生成代码
func main() {
	fmt.Println("=== 从API示例生成代码 ===")

	// 创建输出目录，使用更清晰的名称
	outputDir := "generated_api"
	if err := os.MkdirAll(outputDir, 0755); err != nil {
		log.Fatalf("创建输出目录失败: %v", err)
	}

	// 创建API定义
	apiDef := newapi.NewAPI(newapi.APIInfo{
		Title:       "示例API",
		Description: "从api_example.go生成的API",
		Version:     "1.0.0",
		Author:      "NewAPI Generator",
		Email:       "<EMAIL>",
		Host:        "localhost:8080",
		BasePath:    "/api/v1",
	})

	// 调用API定义函数
	api.UserAPI(apiDef)
	api.ProductAPI(apiDef)

	// 创建生成器，使用api作为包名
	generator := newapi.NewGenerator(apiDef, outputDir, "api")

	// 生成代码
	fmt.Println("正在生成代码...")
	if err := generator.Generate(); err != nil {
		log.Fatalf("代码生成失败: %v", err)
	}

	fmt.Printf("代码生成成功！输出目录: %s\n", filepath.Join(".", outputDir))

	// 列出生成的文件
	files, err := os.ReadDir(outputDir)
	if err != nil {
		log.Fatalf("读取输出目录失败: %v", err)
	}

	fmt.Println("生成的文件:")
	for _, file := range files {
		fmt.Printf("  - %s\n", file.Name())
	}

	fmt.Println("\n=== 生成完成 ===")
	fmt.Printf("要运行生成的API服务器，请执行以下命令:\n")
	fmt.Printf("  cd %s && go run main.go\n", outputDir)
}
