package main

import (
	"net/http"
	"os"
	"testing"

	"admin-amis-zero/newapi"
)

// TestAPIGeneration 测试API代码生成
func TestAPIGeneration(t *testing.T) {
	// 创建临时目录用于测试
	tempDir, err := os.MkdirTemp("", "newapi_test")
	if err != nil {
		t.Fatalf("创建临时目录失败: %v", err)
	}
	defer os.RemoveAll(tempDir)

	// 创建示例API定义
	api := newapi.NewAPI(newapi.APIInfo{
		Title:       "测试API",
		Description: "用于测试NewAPI代码生成功能",
		Version:     "1.0.0",
		Author:      "Test User",
		Email:       "<EMAIL>",
		Host:        "localhost:8080",
		BasePath:    "/api/v1",
	})

	// 添加安全定义
	api.AddSecurity("BearerAuth", newapi.SecurityDefinition{
		Type:        "apiKey",
		Description: "JWT Token",
		Name:        "Authorization",
		In:          "header",
	})

	// 添加中间件
	api.AddMiddleware("test", func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 简单的中间件示例
			next.ServeHTTP(w, r)
		}
	})

	// 创建用户组
	userGroup := newapi.NewGroup("/users").
		WithTags("用户管理")

	// 添加用户相关端点
	userGroup.GET("/:id").
		Handler("GetUser").
		Summary("获取用户详情").
		Description("根据用户ID获取用户详细信息").
		Tags("用户管理").
		Request(&newapi.IdReq{}).
		Response(&newapi.CommonResp{}).
		Build()

	userGroup.POST("/").
		Handler("CreateUser").
		Summary("创建用户").
		Description("创建一个新的用户").
		Tags("用户管理").
		Request(&struct {
			Name  string `json:"name" validate:"required"`
			Email string `json:"email" validate:"required,email"`
		}{}).
		Response(&newapi.CommonResp{}).
		Build()

	// 将用户组添加到API
	api.AddGroup(*userGroup)

	// 注意：这里我们不实际调用生成器，因为生成器的实现不在这个项目中
	// 我们只是验证API定义是有效的
	if err := api.Validate(); err != nil {
		t.Errorf("API验证失败: %v", err)
	}
}

// TestDirectoryAnalysis 测试目录结构分析
func TestDirectoryAnalysis(t *testing.T) {
	// 获取当前目录
	currentDir, err := os.Getwd()
	if err != nil {
		t.Skipf("无法获取当前目录: %v", err)
	}

	utils := newapi.NewUtils()
	analysis, err := utils.AnalyzeDirectoryStructure(currentDir)
	if err != nil {
		t.Fatalf("目录分析失败: %v", err)
	}

	// 验证分析结果
	if analysis.RootPath != currentDir {
		t.Errorf("根路径不匹配，期望: %s, 实际: %s", currentDir, analysis.RootPath)
	}

	if analysis.TotalFiles <= 0 {
		t.Error("应该至少分析到一个文件")
	}

	if len(analysis.Modules) <= 0 {
		t.Error("应该至少分析到一个模块")
	}

	// 检查是否包含Go文件
	goFiles, exists := analysis.FilesByType[".go"]
	if !exists || len(goFiles) <= 0 {
		t.Error("应该至少分析到一个Go文件")
	}
}

// TestModuleDefinition 测试模块定义
func TestModuleDefinition(t *testing.T) {
	module := &newapi.ModuleDefinition{
		Name:        "test_module",
		Path:        "/test/path",
		Description: "测试模块",
		Package:     "test",
		Types:       make([]*newapi.TypeDefinition, 0),
		Groups:      make([]*newapi.GroupDefinition, 0),
		Endpoints:   make([]*newapi.EndpointDefinition, 0),
		SubModules:  make([]*newapi.ModuleDefinition, 0),
		Files:       []string{"test.go"},
		DependsOn:   []string{"dependency1"},
	}

	if module.Name != "test_module" {
		t.Errorf("模块名称不匹配，期望: test_module, 实际: %s", module.Name)
	}

	if len(module.Files) != 1 {
		t.Errorf("文件数量不匹配，期望: 1, 实际: %d", len(module.Files))
	}
}

// TestAPIDocumentationGeneration 测试API文档生成
func TestAPIDocumentationGeneration(t *testing.T) {
	utils := newapi.NewUtils()

	// 创建测试模块
	modules := make(map[string]*newapi.ModuleDefinition)
	modules["test_module"] = &newapi.ModuleDefinition{
		Name:        "test_module",
		Path:        "/test/path",
		Description: "测试模块",
		Package:     "test",
		Types: []*newapi.TypeDefinition{
			{
				Name:     "TestType",
				IsStruct: true,
				Fields:   make([]*newapi.FieldDefinition, 0),
			},
		},
		Groups: []*newapi.GroupDefinition{
			{
				Name:   "TestGroup",
				Prefix: "/api/test",
			},
		},
		Endpoints: make([]*newapi.EndpointDefinition, 0),
		Files:     []string{"test.go"},
	}

	doc := utils.GenerateModularAPIDoc(modules)

	if doc == "" {
		t.Error("生成的文档不应该为空")
	}

	// 检查文档中是否包含模块信息
	if !containsString(doc, "test_module") {
		t.Error("文档应该包含模块名称")
	}
}

// containsString 检查字符串是否包含子字符串
func containsString(s, substr string) bool {
	return len(s) >= len(substr) &&
		(s == substr ||
			containsString(s[1:], substr) ||
			(len(s) > 0 && s[:len(substr)] == substr))
}
