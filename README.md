# NewAPI - 类型安全的API定义系统

## 概述

NewAPI是一个使用Go代码来定义API的系统，替代go-zero的api文件格式。它提供完整的IDE支持，包括代码提示、类型检查、语法高亮等功能，让API定义更加安全和便捷。

## 特性

- ✅ **类型安全**: 使用Go的类型系统确保API定义的正确性
- ✅ **IDE支持**: 完整的代码提示、自动补全、语法检查
- ✅ **代码生成**: 自动生成路由、处理器、中间件等代码
- ✅ **文档生成**: 自动生成Swagger文档和API文档
- ✅ **中间件支持**: 灵活的中间件定义和管理
- ✅ **链式API**: 流畅的链式API设计，易于使用
- ✅ **验证**: 内置API定义验证机制

## 安装

```go
go get your-project/newapi
```

## 快速开始

### 1. 定义API基本信息

```go
package main

import "your-project/newapi"

// 定义API基本信息
apiInfo := newapi.APIInfo{
    Title:       "用户管理系统API",
    Description: "一个完整的用户管理系统API",
    Version:     "v1.0.0",
    Author:      "开发团队",
    Email:       "<EMAIL>",
    Host:        "localhost:8080",
    BasePath:    "/api/v1",
}

// 创建API实例
api := newapi.NewAPI(apiInfo)
```

### 2. 定义请求和响应类型

```go
// 定义请求类型
type LoginRequest struct {
    Username string `json:"username" validate:"required"`
    Password string `json:"password" validate:"required"`
}

// 定义响应类型
type LoginResponse struct {
    Token    string   `json:"token"`
    UserInfo UserInfo `json:"user_info"`
}

type UserInfo struct {
    ID       int64    `json:"id"`
    Username string   `json:"username"`
    Email    string   `json:"email"`
    Roles    []string `json:"roles"`
}
```

### 3. 定义路由组和端点

```go
// 创建认证路由组
authGroup := newapi.NewGroup("/auth").
    WithTags("认证管理")

// 添加登录端点
authGroup.POST("/login").
    Handler("LoginHandler").
    Summary("用户登录").
    Description("用户使用用户名和密码登录系统").
    Request(LoginRequest{}).
    Response(LoginResponse{}).
    Tags("认证").
    Build()

// 将路由组添加到API
api.AddGroup(*authGroup)
```

### 4. 生成代码

```go
// 验证API定义
if err := api.Validate(); err != nil {
    log.Fatalf("API定义验证失败: %v", err)
}

// 生成代码
generator := newapi.NewGenerator(api, "./generated", "api")
if err := generator.Generate(); err != nil {
    log.Fatalf("代码生成失败: %v", err)
}
```

## 详细使用指南

### API信息定义

```go
type APIInfo struct {
    Title       string // API标题
    Description string // API描述
    Version     string // API版本
    Author      string // 作者
    Email       string // 联系邮箱
    Host        string // 主机地址
    BasePath    string // 基础路径
}
```

### 路由组定义

```go
// 创建路由组
userGroup := newapi.NewGroup("/users").
    WithTags("用户管理").                    // 添加标签
    WithMiddleware(authMiddleware, logMiddleware) // 添加中间件

// 添加子路由组
adminGroup := newapi.NewGroup("/admin").
    WithTags("管理员功能")

userGroup.AddSubGroup(*adminGroup)
```

### 端点定义

支持所有HTTP方法：

```go
group.GET("/users")        // GET请求
group.POST("/users")       // POST请求
group.PUT("/users/:id")    // PUT请求
group.DELETE("/users/:id") // DELETE请求
group.PATCH("/users/:id")  // PATCH请求
```

完整的端点定义：

```go
group.POST("/users").
    Handler("CreateUserHandler").           // 处理器名称
    Summary("创建用户").                    // 端点摘要
    Description("创建新用户账户").          // 详细描述
    Request(CreateUserRequest{}).           // 请求类型
    Response(CreateUserResponse{}).         // 响应类型
    Tags("用户管理", "CRUD").              // 标签
    Security("Bearer").                     // 安全要求
    Middleware(validationMiddleware).       // 端点级中间件
    Build()                                 // 构建端点
```

### 中间件定义

#### 预定义中间件

```go
// 认证中间件
authMiddleware := newapi.AuthMiddleware{
    TokenHeader:  "Authorization",
    TokenQuery:   "token",
    SkipPaths:    []string{"/login", "/register"},
    RequiredRole: []string{"user"},
}

// CORS中间件
corsMiddleware := newapi.CORSMiddleware{
    AllowOrigins:     []string{"*"},
    AllowMethods:     []string{"GET", "POST", "PUT", "DELETE"},
    AllowHeaders:     []string{"Content-Type", "Authorization"},
    AllowCredentials: true,
    MaxAge:           86400,
}

// 限流中间件
rateLimitMiddleware := newapi.RateLimitMiddleware{
    Rate:   100,
    Burst:  200,
    Window: 60,
    KeyFunc: "ip",
}
```

#### 自定义中间件

```go
// 定义自定义中间件
customMiddleware := func(next http.HandlerFunc) http.HandlerFunc {
    return func(w http.ResponseWriter, r *http.Request) {
        // 中间件逻辑
        next.ServeHTTP(w, r)
    }
}

// 注册中间件
api.AddMiddleware("custom", customMiddleware)
```

### 安全定义

```go
// JWT认证
api.AddSecurity("Bearer", newapi.SecurityDefinition{
    Type:        "apiKey",
    Description: "JWT Token认证",
    Name:        "Authorization",
    In:          "header",
})

// API Key认证
api.AddSecurity("ApiKey", newapi.SecurityDefinition{
    Type:        "apiKey",
    Description: "API Key认证",
    Name:        "X-API-Key",
    In:          "header",
})
```

### 内置类型

#### 通用请求类型

```go
// 分页请求
type PageReq struct {
    Page      int    `form:"page,default=1"`
    PageSize  int    `form:"page_size,default=10"`
    Keyword   string `form:"keyword"`
    SortBy    string `form:"sort_by"`
    SortOrder string `form:"sort_order,options=asc|desc"`
}

// ID请求
type IdReq struct {
    Id int64 `uri:"id" validate:"required,min=1"`
}

// 批量ID请求
type BatchIdReq struct {
    Ids []int64 `json:"ids" validate:"required,min=1"`
}
```

#### 通用响应类型

```go
// 通用响应
type CommonResp struct {
    Code    int         `json:"code"`
    Message string      `json:"message"`
    Data    interface{} `json:"data,omitempty"`
}

// 分页响应
type PageResp struct {
    Total      int64       `json:"total"`
    Page       int         `json:"page"`
    PageSize   int         `json:"page_size"`
    TotalPages int         `json:"total_pages"`
    HasNext    bool        `json:"has_next"`
    HasPrev    bool        `json:"has_prev"`
    Data       interface{} `json:"data"`
}
```

### 代码生成

#### 生成器配置

```go
generator := newapi.NewGenerator(api, outputDir, packageName)
```

#### 生成的文件

- `types.go` - 类型定义
- `handlers.go` - 处理器函数
- `routes.go` - 路由配置
- `middlewares.go` - 中间件函数
- `main.go` - 主程序入口

### 工具函数

#### 生成Swagger文档

```go
utils := newapi.NewUtils()
swagger, err := utils.GenerateSwagger(api)
if err != nil {
    log.Fatal(err)
}

// 保存为JSON文件
swaggerJSON, _ := json.MarshalIndent(swagger, "", "  ")
ioutil.WriteFile("swagger.json", swaggerJSON, 0644)
```

#### 生成API文档

```go
doc := utils.GenerateAPIDoc(api)
ioutil.WriteFile("api.md", []byte(doc), 0644)
```

#### 导出API定义

```go
apiJSON, err := utils.ExportToJSON(api)
if err != nil {
    log.Fatal(err)
}
ioutil.WriteFile("api-definition.json", apiJSON, 0644)
```

## 高级特性

### 验证标签

使用内置的验证工具：

```go
type CreateUserRequest struct {
    Username string `json:"username" validate:"required,min=3,max=50"`
    Email    string `json:"email" validate:"required,email"`
    Age      int    `json:"age" validate:"min=18,max=120"`
    Role     string `json:"role" validate:"oneof=admin user guest"`
}

// 使用验证工具
validate := newapi.Validate
username := validate.Required() + "," + validate.MinLength(3) + "," + validate.MaxLength(50)
```

### 中间件链

```go
// 创建中间件链
chain := newapi.NewMiddlewareChain(
    corsMiddleware,
    authMiddleware,
    rateLimitMiddleware,
).Add(customMiddleware)

// 应用中间件链
finalHandler := chain.Execute(yourHandler)
```

### 路径参数

```go
// 定义路径参数
type GetUserRequest struct {
    Id int64 `uri:"id" validate:"required,min=1"`
}

// 使用路径参数
userGroup.GET("/:id").
    Request(GetUserRequest{}).
    Handler("GetUserHandler").
    Build()
```

## 最佳实践

### 1. 文件组织

建议按功能模块组织API定义：

```
api/
├── auth/
│   └── auth_api.go
├── user/
│   └── user_api.go
├── file/
│   └── file_api.go
└── main.go
```

### 2. 类型定义

将共用的类型定义在单独的包中：

```go
package types

type User struct {
    ID       int64  `json:"id"`
    Username string `json:"username"`
    Email    string `json:"email"`
}
```

### 3. 中间件管理

创建中间件工厂函数：

```go
func CreateAuthMiddleware(config AuthConfig) newapi.MiddlewareFunc {
    return func(next http.HandlerFunc) http.HandlerFunc {
        return func(w http.ResponseWriter, r *http.Request) {
            // 认证逻辑
            next.ServeHTTP(w, r)
        }
    }
}
```

### 4. 错误处理

定义统一的错误响应格式：

```go
type ErrorResponse struct {
    Code    int    `json:"code"`
    Message string `json:"message"`
    Details string `json:"details,omitempty"`
}
```

## 与go-zero对比

| 特性 | go-zero api | NewAPI |
|------|-------------|---------|
| 代码提示 | ❌ | ✅ |
| 类型检查 | ❌ | ✅ |
| 语法高亮 | 有限 | ✅ |
| 重构支持 | ❌ | ✅ |
| 导入管理 | ❌ | ✅ |
| 自动补全 | ❌ | ✅ |
| 编译时验证 | ❌ | ✅ |
| 文档生成 | ✅ | ✅ |
| 代码生成 | ✅ | ✅ |
| 学习成本 | 低 | 中等 |

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

MIT License