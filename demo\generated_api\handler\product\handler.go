package product

import (
	"generated_api/logic/product"
	"github.com/gin-gonic/gin"
	"net/http"
)

// ListProducts 获取产品列表
func ListProducts(c *gin.Context) {
	// 调用logic层处理业务逻辑
	result, err := product.ListProducts()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, result)
}

// GetProduct 获取产品详情
func GetProduct(c *gin.Context) {
	// 调用logic层处理业务逻辑
	result, err := product.GetProduct()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, result)
}
