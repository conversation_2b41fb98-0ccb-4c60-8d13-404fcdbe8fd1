// Package newapi 提供了一个类型安全的API定义系统
// 替代go-zero的api文件，提供代码提示和类型检查
package newapi

import (
	"fmt"
	"net/http"
	"reflect"
	"strings"
)

// APIInfo 定义API基本信息
type APIInfo struct {
	Title       string
	Description string
	Version     string
	Author      string
	Email       string
	Host        string
	BasePath    string
}

// HTTPMethod 定义HTTP方法类型
type HTTPMethod string

const (
	GET     HTTPMethod = "GET"
	POST    HTTPMethod = "POST"
	PUT     HTTPMethod = "PUT"
	DELETE  HTTPMethod = "DELETE"
	PATCH   HTTPMethod = "PATCH"
	HEAD    HTTPMethod = "HEAD"
	OPTIONS HTTPMethod = "OPTIONS"
)

// Endpoint 定义API端点
type Endpoint struct {
	Method       HTTPMethod
	Path         string
	Handler      string
	Summary      string
	Description  string
	Tags         []string
	RequestType  interface{}
	ResponseType interface{}
	Middlewares  []MiddlewareFunc
	Security     []string
}

// MiddlewareFunc 定义中间件函数类型
type MiddlewareFunc func(http.HandlerFunc) http.HandlerFunc

// Group 定义路由组
type Group struct {
	Prefix      string
	Middlewares []MiddlewareFunc
	Tags        []string
	Endpoints   []Endpoint
	SubGroups   []Group
}

// API 定义完整的API规范
type API struct {
	Info        APIInfo
	Groups      []Group
	Endpoints   []Endpoint
	Middlewares map[string]MiddlewareFunc
	Security    map[string]SecurityDefinition
}

// SecurityDefinition 定义安全配置
type SecurityDefinition struct {
	Type        string
	Description string
	Name        string
	In          string
}

// NewAPI 创建新的API实例
func NewAPI(info APIInfo) *API {
	return &API{
		Info:        info,
		Groups:      make([]Group, 0),
		Endpoints:   make([]Endpoint, 0),
		Middlewares: make(map[string]MiddlewareFunc),
		Security:    make(map[string]SecurityDefinition),
	}
}

// AddGroup 添加路由组
func (a *API) AddGroup(group Group) *API {
	a.Groups = append(a.Groups, group)
	return a
}

// AddEndpoint 添加单个端点
func (a *API) AddEndpoint(endpoint Endpoint) *API {
	a.Endpoints = append(a.Endpoints, endpoint)
	return a
}

// AddMiddleware 注册中间件
func (a *API) AddMiddleware(name string, middleware MiddlewareFunc) *API {
	a.Middlewares[name] = middleware
	return a
}

// AddSecurity 添加安全定义
func (a *API) AddSecurity(name string, security SecurityDefinition) *API {
	a.Security[name] = security
	return a
}

// NewGroup 创建新的路由组
func NewGroup(prefix string) *Group {
	return &Group{
		Prefix:      prefix,
		Middlewares: make([]MiddlewareFunc, 0),
		Tags:        make([]string, 0),
		Endpoints:   make([]Endpoint, 0),
		SubGroups:   make([]Group, 0),
	}
}

// WithMiddleware 为组添加中间件
func (g *Group) WithMiddleware(middleware ...MiddlewareFunc) *Group {
	g.Middlewares = append(g.Middlewares, middleware...)
	return g
}

// WithTags 为组添加标签
func (g *Group) WithTags(tags ...string) *Group {
	g.Tags = append(g.Tags, tags...)
	return g
}

// AddEndpoint 为组添加端点
func (g *Group) AddEndpoint(endpoint Endpoint) *Group {
	g.Endpoints = append(g.Endpoints, endpoint)
	return g
}

// AddSubGroup 添加子组
func (g *Group) AddSubGroup(subGroup Group) *Group {
	g.SubGroups = append(g.SubGroups, subGroup)
	return g
}

// GET 创建GET请求端点
func (g *Group) GET(path string) *EndpointBuilder {
	return &EndpointBuilder{
		endpoint: Endpoint{
			Method: GET,
			Path:   path,
		},
		group: g,
	}
}

// POST 创建POST请求端点
func (g *Group) POST(path string) *EndpointBuilder {
	return &EndpointBuilder{
		endpoint: Endpoint{
			Method: POST,
			Path:   path,
		},
		group: g,
	}
}

// PUT 创建PUT请求端点
func (g *Group) PUT(path string) *EndpointBuilder {
	return &EndpointBuilder{
		endpoint: Endpoint{
			Method: PUT,
			Path:   path,
		},
		group: g,
	}
}

// DELETE 创建DELETE请求端点
func (g *Group) DELETE(path string) *EndpointBuilder {
	return &EndpointBuilder{
		endpoint: Endpoint{
			Method: DELETE,
			Path:   path,
		},
		group: g,
	}
}

// PATCH 创建PATCH请求端点
func (g *Group) PATCH(path string) *EndpointBuilder {
	return &EndpointBuilder{
		endpoint: Endpoint{
			Method: PATCH,
			Path:   path,
		},
		group: g,
	}
}

// EndpointBuilder 端点构建器
type EndpointBuilder struct {
	endpoint Endpoint
	group    *Group
}

// Handler 设置处理器名称
func (eb *EndpointBuilder) Handler(handler string) *EndpointBuilder {
	eb.endpoint.Handler = handler
	return eb
}

// Summary 设置摘要
func (eb *EndpointBuilder) Summary(summary string) *EndpointBuilder {
	eb.endpoint.Summary = summary
	return eb
}

// Description 设置描述
func (eb *EndpointBuilder) Description(description string) *EndpointBuilder {
	eb.endpoint.Description = description
	return eb
}

// Tags 设置标签
func (eb *EndpointBuilder) Tags(tags ...string) *EndpointBuilder {
	eb.endpoint.Tags = tags
	return eb
}

// Request 设置请求类型
func (eb *EndpointBuilder) Request(requestType interface{}) *EndpointBuilder {
	eb.endpoint.RequestType = requestType
	return eb
}

// Response 设置响应类型
func (eb *EndpointBuilder) Response(responseType interface{}) *EndpointBuilder {
	eb.endpoint.ResponseType = responseType
	return eb
}

// Middleware 添加中间件
func (eb *EndpointBuilder) Middleware(middleware ...MiddlewareFunc) *EndpointBuilder {
	eb.endpoint.Middlewares = append(eb.endpoint.Middlewares, middleware...)
	return eb
}

// Security 设置安全要求
func (eb *EndpointBuilder) Security(security ...string) *EndpointBuilder {
	eb.endpoint.Security = security
	return eb
}

// Build 构建端点并添加到组
func (eb *EndpointBuilder) Build() *Group {
	eb.group.AddEndpoint(eb.endpoint)
	return eb.group
}

// GetTypeName 获取类型名称
func GetTypeName(t interface{}) string {
	if t == nil {
		return ""
	}

	typ := reflect.TypeOf(t)
	if typ.Kind() == reflect.Ptr {
		typ = typ.Elem()
	}

	return typ.Name()
}

// Validate 验证API定义
func (a *API) Validate() error {
	if a.Info.Title == "" {
		return fmt.Errorf("API title is required")
	}

	if a.Info.Version == "" {
		return fmt.Errorf("API version is required")
	}

	// 验证所有端点
	for _, group := range a.Groups {
		if err := a.validateGroup(group); err != nil {
			return err
		}
	}

	for _, endpoint := range a.Endpoints {
		if err := a.validateEndpoint(endpoint); err != nil {
			return err
		}
	}

	return nil
}

// validateGroup 验证路由组
func (a *API) validateGroup(group Group) error {
	if !strings.HasPrefix(group.Prefix, "/") {
		return fmt.Errorf("group prefix must start with '/'")
	}

	for _, endpoint := range group.Endpoints {
		if err := a.validateEndpoint(endpoint); err != nil {
			return err
		}
	}

	for _, subGroup := range group.SubGroups {
		if err := a.validateGroup(subGroup); err != nil {
			return err
		}
	}

	return nil
}

// validateEndpoint 验证端点
func (a *API) validateEndpoint(endpoint Endpoint) error {
	if endpoint.Method == "" {
		return fmt.Errorf("endpoint method is required")
	}

	if endpoint.Path == "" {
		return fmt.Errorf("endpoint path is required")
	}

	if endpoint.Handler == "" {
		return fmt.Errorf("endpoint handler is required")
	}

	return nil
}
