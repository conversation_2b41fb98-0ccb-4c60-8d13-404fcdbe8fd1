package newapi

import (
	"fmt"
	"go/format"
	"io/ioutil"
	"os"
	"path/filepath"
	"reflect"
	"strings"
	"text/template"
)

// Generator 代码生成器
type Generator struct {
	API         *API
	OutputDir   string
	PackageName string
	ModulePath  string
	Templates   map[string]*template.Template
}

// NewGenerator 创建新的代码生成器
func NewGenerator(api *API, outputDir, packageName string) *Generator {
	// 从输出目录推断模块路径
	modulePath := filepath.Base(outputDir)
	if modulePath == "." || modulePath == "" {
		modulePath = "generated_api"
	}

	return &Generator{
		API:         api,
		OutputDir:   outputDir,
		PackageName: packageName,
		ModulePath:  modulePath,
		Templates:   make(map[string]*template.Template),
	}
}

// Generate 生成所有代码文件
func (g *Generator) Generate() error {
	if err := g.initTemplates(); err != nil {
		return fmt.Errorf("初始化模板失败: %v", err)
	}

	// 创建输出目录
	if err := os.MkdirAll(g.OutputDir, 0755); err != nil {
		return fmt.Errorf("创建输出目录失败: %v", err)
	}

	// 创建模块子目录
	modules := []string{"handler", "logic", "routes", "types", "middleware"}
	for _, module := range modules {
		moduleDir := filepath.Join(g.OutputDir, module)
		if err := os.MkdirAll(moduleDir, 0755); err != nil {
			return fmt.Errorf("创建%s目录失败: %v", module, err)
		}

		// 为handler, logic, routes, types创建子模块目录
		if module != "middleware" {
			subModules := []string{"user", "product"}
			for _, subModule := range subModules {
				subDir := filepath.Join(moduleDir, subModule)
				if err := os.MkdirAll(subDir, 0755); err != nil {
					return fmt.Errorf("创建%s/%s目录失败: %v", module, subModule, err)
				}
			}
		}
	}

	// 生成各模块文件
	if err := g.generateModuleFiles(); err != nil {
		return fmt.Errorf("生成模块文件失败: %v", err)
	}

	// 生成主文件
	if err := g.generateMain(); err != nil {
		return fmt.Errorf("生成主文件失败: %v", err)
	}

	// 生成go.mod文件
	if err := g.generateGoMod(); err != nil {
		return fmt.Errorf("生成go.mod文件失败: %v", err)
	}

	return nil
}

// initTemplates 初始化模板
func (g *Generator) initTemplates() error {
	// 用户类型模板
	userTypesTemplate := `package {{.PackageName}}

// User 用户信息
type User struct {
	ID       int64  ` + "`" + `json:"id" validate:"required"` + "`" + `
	Username string ` + "`" + `json:"username" validate:"required,min=3,max=20"` + "`" + `
	Email    string ` + "`" + `json:"email" validate:"required,email"` + "`" + `
	Phone    string ` + "`" + `json:"phone,omitempty" validate:"phone"` + "`" + `
	Status   int    ` + "`" + `json:"status" validate:"oneof=0 1"` + "`" + `
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string ` + "`" + `json:"username" validate:"required,min=3,max=20"` + "`" + `
	Email    string ` + "`" + `json:"email" validate:"required,email"` + "`" + `
	Phone    string ` + "`" + `json:"phone,omitempty" validate:"phone"` + "`" + `
	Password string ` + "`" + `json:"password" validate:"required,min=6"` + "`" + `
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Username string ` + "`" + `json:"username,omitempty" validate:"min=3,max=20"` + "`" + `
	Email    string ` + "`" + `json:"email,omitempty" validate:"email"` + "`" + `
	Phone    string ` + "`" + `json:"phone,omitempty" validate:"phone"` + "`" + `
	Status   int    ` + "`" + `json:"status,omitempty" validate:"oneof=0 1"` + "`" + `
}
`

	// 产品类型模板
	productTypesTemplate := `package {{.PackageName}}

// Product 产品信息
type Product struct {
	ID          int64  ` + "`" + `json:"id" validate:"required"` + "`" + `
	Name        string ` + "`" + `json:"name" validate:"required,min=1,max=100"` + "`" + `
	Description string ` + "`" + `json:"description,omitempty"` + "`" + `
	Price       int64  ` + "`" + `json:"price" validate:"required,min=0"` + "`" + `
	Status      int    ` + "`" + `json:"status" validate:"oneof=0 1"` + "`" + `
}
`

	// 用户处理器模板
	userHandlerTemplate := `package {{.PackageName}}

import (
	"net/http"
	"github.com/gin-gonic/gin"
	"{{.ModulePath}}/logic/{{.PackageName}}"
)

{{range .Endpoints}}
// {{.Handler}} {{.Handler}}
func {{.Handler}}(c *gin.Context) {
	// 调用logic层处理业务逻辑
	result, err := {{.PackageName}}.{{.Handler}}()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, result)
}
{{end}}`

	// 产品处理器模板
	productHandlerTemplate := `package {{.PackageName}}

import (
	"net/http"
	"github.com/gin-gonic/gin"
	"{{.ModulePath}}/logic/{{.PackageName}}"
)

{{range .Endpoints}}
// {{.Handler}} {{.Handler}}
func {{.Handler}}(c *gin.Context) {
	// 调用logic层处理业务逻辑
	result, err := {{.PackageName}}.{{.Handler}}()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, result)
}
{{end}}`

	// 用户logic模板
	userLogicTemplate := `package {{.PackageName}}

import (
	"{{.ModulePath}}/types/{{.PackageName}}"
)

{{range .Endpoints}}
// {{.Handler}} {{.Handler}}
func {{.Handler}}() (interface{}, error) {
	// TODO: 实现{{.Handler}}的业务逻辑
	// 这里应该调用数据访问层(DAO)来处理数据
	return nil, nil
}
{{end}}`

	// 产品logic模板
	productLogicTemplate := `package {{.PackageName}}

import (
	"{{.ModulePath}}/types/{{.PackageName}}"
)

{{range .Endpoints}}
// {{.Handler}} {{.Handler}}
func {{.Handler}}() (interface{}, error) {
	// TODO: 实现{{.Handler}}的业务逻辑
	// 这里应该调用数据访问层(DAO)来处理数据
	return nil, nil
}
{{end}}`

	// 用户路由模板
	userRouteTemplate := `package {{.PackageName}}

import (
	"{{.ModulePath}}/handler/{{.PackageName}}"
	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册用户路由
func RegisterRoutes(r *gin.RouterGroup) {
	users := r.Group("/users")
	{
		{{range .Endpoints}}
		users.{{.Method}}("{{.Path}}", {{.PackageName}}.{{.Handler}})
		{{end}}
	}
}
`

	// 产品路由模板
	productRouteTemplate := `package {{.PackageName}}

import (
	"{{.ModulePath}}/handler/{{.PackageName}}"
	"github.com/gin-gonic/gin"
)

// RegisterRoutes 注册产品路由
func RegisterRoutes(r *gin.RouterGroup) {
	products := r.Group("/products")
	{
		{{range .Endpoints}}
		products.{{.Method}}("{{.Path}}", {{.PackageName}}.{{.Handler}})
		{{end}}
	}
}`

	// 中间件模板
	middlewareTemplate := `package middleware

import (
	"github.com/gin-gonic/gin"
)

// AuthMiddleware 认证中间件
func AuthMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: 实现认证逻辑
		c.Next()
	}
}

// CORSMiddleware CORS中间件
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// TODO: 实现CORS逻辑
		c.Next()
	}
}
`

	// 主文件模板
	mainTemplate := `package main

import (
	"log"
	"github.com/gin-gonic/gin"
	"{{.ModulePath}}/routes/user"
	"{{.ModulePath}}/routes/product"
)

func main() {
	r := gin.Default()
	
	// 注册路由
	api := r.Group("/api/v1")
	
	// 注册各模块路由
	user.RegisterRoutes(api)
	product.RegisterRoutes(api)
	
	log.Println("服务器启动在 :8080")
	if err := r.Run(":8080"); err != nil {
		log.Fatal("启动服务器失败:", err)
	}
}
`

	// go.mod模板
	gomodTemplate := `module {{.ModulePath}}

go 1.21

require (
	github.com/gin-gonic/gin v1.9.1
)

replace admin-amis-zero/newapi => ../../
`

	var err error

	g.Templates["user_types"], err = template.New("user_types").Parse(userTypesTemplate)
	if err != nil {
		return err
	}

	g.Templates["product_types"], err = template.New("product_types").Parse(productTypesTemplate)
	if err != nil {
		return err
	}

	g.Templates["user_handler"], err = template.New("user_handler").Parse(userHandlerTemplate)
	if err != nil {
		return err
	}

	g.Templates["product_handler"], err = template.New("product_handler").Parse(productHandlerTemplate)
	if err != nil {
		return err
	}

	g.Templates["user_logic"], err = template.New("user_logic").Parse(userLogicTemplate)
	if err != nil {
		return err
	}

	g.Templates["product_logic"], err = template.New("product_logic").Parse(productLogicTemplate)
	if err != nil {
		return err
	}

	g.Templates["user_route"], err = template.New("user_route").Parse(userRouteTemplate)
	if err != nil {
		return err
	}

	g.Templates["product_route"], err = template.New("product_route").Parse(productRouteTemplate)
	if err != nil {
		return err
	}

	g.Templates["middleware"], err = template.New("middleware").Parse(middlewareTemplate)
	if err != nil {
		return err
	}

	g.Templates["main"], err = template.New("main").Parse(mainTemplate)
	if err != nil {
		return err
	}

	g.Templates["gomod"], err = template.New("gomod").Parse(gomodTemplate)
	if err != nil {
		return err
	}

	return nil
}

// generateModuleFiles 生成各模块文件
func (g *Generator) generateModuleFiles() error {
	// 生成用户模块文件
	if err := g.generateUserModule(); err != nil {
		return err
	}

	// 生成产品模块文件
	if err := g.generateProductModule(); err != nil {
		return err
	}

	// 生成中间件文件
	return g.generateMiddlewares()
}

// generateUserModule 生成用户模块文件
func (g *Generator) generateUserModule() error {
	// 提取用户相关的端点信息
	userEndpoints := g.extractEndpointsByGroupPrefix("/users")

	// 生成用户类型文件
	typesData := struct {
		PackageName string
		ModulePath  string
	}{
		PackageName: "user",
		ModulePath:  g.ModulePath,
	}

	if err := g.generateFile(filepath.Join(g.OutputDir, "types", "user", "types.go"), "user_types", typesData); err != nil {
		return err
	}

	// 生成用户logic文件
	logicData := struct {
		PackageName string
		ModulePath  string
		Endpoints   []EndpointInfo
	}{
		PackageName: "user",
		ModulePath:  g.ModulePath,
		Endpoints:   userEndpoints,
	}

	if err := g.generateFile(filepath.Join(g.OutputDir, "logic", "user", "logic.go"), "user_logic", logicData); err != nil {
		return err
	}

	// 生成用户处理器文件
	handlerData := struct {
		PackageName string
		ModulePath  string
		Endpoints   []EndpointInfo
	}{
		PackageName: "user",
		ModulePath:  g.ModulePath,
		Endpoints:   userEndpoints,
	}

	if err := g.generateFile(filepath.Join(g.OutputDir, "handler", "user", "handler.go"), "user_handler", handlerData); err != nil {
		return err
	}

	// 生成用户路由文件
	routeData := struct {
		PackageName string
		ModulePath  string
		Endpoints   []EndpointInfo
	}{
		PackageName: "user",
		ModulePath:  g.ModulePath,
		Endpoints:   userEndpoints,
	}

	return g.generateFile(filepath.Join(g.OutputDir, "routes", "user", "route.go"), "user_route", routeData)
}

// generateProductModule 生成产品模块文件
func (g *Generator) generateProductModule() error {
	// 提取产品相关的端点信息
	productEndpoints := g.extractEndpointsByGroupPrefix("/products")

	// 生成产品类型文件
	typesData := struct {
		PackageName string
		ModulePath  string
	}{
		PackageName: "product",
		ModulePath:  g.ModulePath,
	}

	if err := g.generateFile(filepath.Join(g.OutputDir, "types", "product", "types.go"), "product_types", typesData); err != nil {
		return err
	}

	// 生成产品logic文件
	logicData := struct {
		PackageName string
		ModulePath  string
		Endpoints   []EndpointInfo
	}{
		PackageName: "product",
		ModulePath:  g.ModulePath,
		Endpoints:   productEndpoints,
	}

	if err := g.generateFile(filepath.Join(g.OutputDir, "logic", "product", "logic.go"), "product_logic", logicData); err != nil {
		return err
	}

	// 生成产品处理器文件
	handlerData := struct {
		PackageName string
		ModulePath  string
		Endpoints   []EndpointInfo
	}{
		PackageName: "product",
		ModulePath:  g.ModulePath,
		Endpoints:   productEndpoints,
	}

	if err := g.generateFile(filepath.Join(g.OutputDir, "handler", "product", "handler.go"), "product_handler", handlerData); err != nil {
		return err
	}

	// 生成产品路由文件
	routeData := struct {
		PackageName string
		ModulePath  string
		Endpoints   []EndpointInfo
	}{
		PackageName: "product",
		ModulePath:  g.ModulePath,
		Endpoints:   productEndpoints,
	}

	return g.generateFile(filepath.Join(g.OutputDir, "routes", "product", "route.go"), "product_route", routeData)
}

// generateMiddlewares 生成中间件文件
func (g *Generator) generateMiddlewares() error {
	data := struct {
	}{}

	return g.generateFile(filepath.Join(g.OutputDir, "middleware", "middleware.go"), "middleware", data)
}

// generateMain 生成主文件
func (g *Generator) generateMain() error {
	data := struct {
		ModulePath string
	}{
		ModulePath: g.ModulePath,
	}

	return g.generateFile(filepath.Join(g.OutputDir, "main.go"), "main", data)
}

// generateGoMod 生成go.mod文件
func (g *Generator) generateGoMod() error {
	data := struct {
		ModulePath string
	}{
		ModulePath: g.ModulePath,
	}

	return g.generateFile(filepath.Join(g.OutputDir, "go.mod"), "gomod", data)
}

// generateFile 生成单个文件
func (g *Generator) generateFile(filename, templateName string, data interface{}) error {
	tmpl, exists := g.Templates[templateName]
	if !exists {
		return fmt.Errorf("模板 %s 不存在", templateName)
	}

	var buf strings.Builder
	if err := tmpl.Execute(&buf, data); err != nil {
		return fmt.Errorf("执行模板失败: %v", err)
	}

	// 格式化Go代码
	formatted, err := format.Source([]byte(buf.String()))
	if err != nil {
		// 如果格式化失败，使用原始代码
		formatted = []byte(buf.String())
	}

	// 确保文件所在的目录存在
	dir := filepath.Dir(filename)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("创建目录失败: %v", err)
	}

	return ioutil.WriteFile(filename, formatted, 0644)
}

// TypeInfo 类型信息
type TypeInfo struct {
	Name    string
	Comment string
	Fields  []FieldInfo
}

// FieldInfo 字段信息
type FieldInfo struct {
	Name    string
	Type    string
	Tag     string
	Comment string
}

// HandlerInfo 处理器信息
type HandlerInfo struct {
	Name         string
	Comment      string
	RequestType  string
	ResponseType string
}

// GroupInfo 路由组信息
type GroupInfo struct {
	Name      string
	Prefix    string
	Endpoints []EndpointInfo
}

// EndpointInfo 端点信息
type EndpointInfo struct {
	Name    string
	Method  string
	Path    string
	Handler string
}

// MiddlewareInfo 中间件信息
type MiddlewareInfo struct {
	Name    string
	Comment string
}

// extractTypes 提取所有类型信息
func (g *Generator) extractTypes() []TypeInfo {
	var types []TypeInfo
	typeMap := make(map[string]bool)

	// 从端点中提取类型
	for _, group := range g.API.Groups {
		for _, endpoint := range group.Endpoints {
			if endpoint.RequestType != nil {
				if typeName := GetTypeName(endpoint.RequestType); typeName != "" && !typeMap[typeName] {
					types = append(types, g.analyzeType(endpoint.RequestType))
					typeMap[typeName] = true
				}
			}
			if endpoint.ResponseType != nil {
				if typeName := GetTypeName(endpoint.ResponseType); typeName != "" && !typeMap[typeName] {
					types = append(types, g.analyzeType(endpoint.ResponseType))
					typeMap[typeName] = true
				}
			}
		}
	}

	for _, endpoint := range g.API.Endpoints {
		if endpoint.RequestType != nil {
			if typeName := GetTypeName(endpoint.RequestType); typeName != "" && !typeMap[typeName] {
				types = append(types, g.analyzeType(endpoint.RequestType))
				typeMap[typeName] = true
			}
		}
		if endpoint.ResponseType != nil {
			if typeName := GetTypeName(endpoint.ResponseType); typeName != "" && !typeMap[typeName] {
				types = append(types, g.analyzeType(endpoint.ResponseType))
				typeMap[typeName] = true
			}
		}
	}

	return types
}

// analyzeType 分析类型结构
func (g *Generator) analyzeType(t interface{}) TypeInfo {
	typ := reflect.TypeOf(t)
	if typ.Kind() == reflect.Ptr {
		typ = typ.Elem()
	}

	typeInfo := TypeInfo{
		Name:   typ.Name(),
		Fields: make([]FieldInfo, 0),
	}

	if typ.Kind() == reflect.Struct {
		for i := 0; i < typ.NumField(); i++ {
			field := typ.Field(i)
			fieldInfo := FieldInfo{
				Name: field.Name,
				Type: field.Type.String(),
				Tag:  fmt.Sprintf("`%s`", field.Tag),
			}
			typeInfo.Fields = append(typeInfo.Fields, fieldInfo)
		}
	}

	return typeInfo
}

// extractHandlers 提取处理器信息
func (g *Generator) extractHandlers() []HandlerInfo {
	var handlers []HandlerInfo
	handlerMap := make(map[string]bool)

	for _, group := range g.API.Groups {
		for _, endpoint := range group.Endpoints {
			if !handlerMap[endpoint.Handler] {
				handler := HandlerInfo{
					Name:    endpoint.Handler,
					Comment: endpoint.Summary,
				}
				if endpoint.RequestType != nil {
					handler.RequestType = GetTypeName(endpoint.RequestType)
				}
				if endpoint.ResponseType != nil {
					handler.ResponseType = GetTypeName(endpoint.ResponseType)
				}
				handlers = append(handlers, handler)
				handlerMap[endpoint.Handler] = true
			}
		}
	}

	for _, endpoint := range g.API.Endpoints {
		if !handlerMap[endpoint.Handler] {
			handler := HandlerInfo{
				Name:    endpoint.Handler,
				Comment: endpoint.Summary,
			}
			if endpoint.RequestType != nil {
				handler.RequestType = GetTypeName(endpoint.RequestType)
			}
			if endpoint.ResponseType != nil {
				handler.ResponseType = GetTypeName(endpoint.ResponseType)
			}
			handlers = append(handlers, handler)
			handlerMap[endpoint.Handler] = true
		}
	}

	return handlers
}

// extractGroups 提取路由组信息
func (g *Generator) extractGroups() []GroupInfo {
	var groups []GroupInfo

	for _, group := range g.API.Groups {
		groupInfo := GroupInfo{
			Name:      strings.ReplaceAll(group.Prefix[1:], "/", "_") + "Group",
			Prefix:    group.Prefix,
			Endpoints: make([]EndpointInfo, 0),
		}

		for _, endpoint := range group.Endpoints {
			endpointInfo := EndpointInfo{
				Name:    strings.ReplaceAll(group.Prefix[1:], "/", "_") + "Group",
				Method:  strings.ToUpper(string(endpoint.Method)),
				Path:    endpoint.Path,
				Handler: endpoint.Handler,
			}
			groupInfo.Endpoints = append(groupInfo.Endpoints, endpointInfo)
		}

		groups = append(groups, groupInfo)
	}

	return groups
}

// extractEndpoints 提取独立端点信息
func (g *Generator) extractEndpoints() []EndpointInfo {
	var endpoints []EndpointInfo

	for _, endpoint := range g.API.Endpoints {
		endpointInfo := EndpointInfo{
			Method:  strings.ToUpper(string(endpoint.Method)),
			Path:    endpoint.Path,
			Handler: endpoint.Handler,
		}
		endpoints = append(endpoints, endpointInfo)
	}

	return endpoints
}

// extractEndpointsByGroupPrefix 根据组前缀提取端点信息
func (g *Generator) extractEndpointsByGroupPrefix(prefix string) []EndpointInfo {
	var endpoints []EndpointInfo

	for _, group := range g.API.Groups {
		if group.Prefix == prefix {
			for _, endpoint := range group.Endpoints {
				endpointInfo := EndpointInfo{
					Name:    endpoint.Handler,
					Method:  string(endpoint.Method),
					Path:    endpoint.Path,
					Handler: endpoint.Handler,
				}
				endpoints = append(endpoints, endpointInfo)
			}
			break
		}
	}

	return endpoints
}

// extractMiddlewares 提取中间件信息
func (g *Generator) extractMiddlewares() []MiddlewareInfo {
	var middlewares []MiddlewareInfo

	for name := range g.API.Middlewares {
		middleware := MiddlewareInfo{
			Name:    name,
			Comment: fmt.Sprintf("%s中间件", name),
		}
		middlewares = append(middlewares, middleware)
	}

	return middlewares
}
