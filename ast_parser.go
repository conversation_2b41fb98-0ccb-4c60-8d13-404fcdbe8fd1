package newapi

import (
	"fmt"
	"go/ast"
	"go/parser"
	"go/token"
	"log"
	"os"
	"path/filepath"
	"reflect"
	"strconv"
	"strings"
)

// ASTParser AST解析器
type ASTParser struct {
	FileSet     *token.FileSet
	Files       map[string]*ast.File
	TypeInfo    map[string]*TypeDefinition
	APIInfo     *APIInfo
	Groups      []*GroupDefinition
	Endpoints   []*EndpointDefinition
	Middlewares map[string]*MiddlewareDefinition
	// 新增字段：模块和目录信息
	Modules       map[string]*ModuleDefinition `json:"modules"`        // 模块信息
	RootPath      string                       `json:"root_path"`      // 根目录路径
	CurrentModule string                       `json:"current_module"` // 当前正在处理的模块
}

// TypeDefinition 类型定义信息
type TypeDefinition struct {
	Name        string
	Package     string
	Fields      []*FieldDefinition
	Methods     []*MethodDefinition
	IsStruct    bool
	IsInterface bool
	Comment     string
	Tags        map[string]string
	// 新增字段：文件和模块信息
	FilePath     string `json:"file_path"`     // 文件路径
	Directory    string `json:"directory"`     // 所在目录
	Module       string `json:"module"`        // 所属模块
	RelativePath string `json:"relative_path"` // 相对路径
}

// FieldDefinition 字段定义信息
type FieldDefinition struct {
	Name     string
	Type     string
	JSONTag  string
	FormTag  string
	ValidTag string
	Comment  string
	Optional bool
}

// MethodDefinition 方法定义信息
type MethodDefinition struct {
	Name       string
	Receiver   string
	Parameters []*ParameterDefinition
	Results    []*ParameterDefinition
	Comment    string
}

// ParameterDefinition 参数定义信息
type ParameterDefinition struct {
	Name string
	Type string
}

// GroupDefinition 路由组定义信息
type GroupDefinition struct {
	Name        string
	Prefix      string
	Tags        []string
	Middlewares []string
	Endpoints   []*EndpointDefinition
	Comment     string
	// 新增字段：模块和目录信息
	FilePath     string `json:"file_path"`     // 文件路径
	Directory    string `json:"directory"`     // 所在目录
	Module       string `json:"module"`        // 所属模块
	RelativePath string `json:"relative_path"` // 相对路径
}

// EndpointDefinition 端点定义信息
type EndpointDefinition struct {
	Method       string
	Path         string
	Handler      string
	Summary      string
	Description  string
	Tags         []string
	RequestType  *TypeDefinition
	ResponseType *TypeDefinition
	Middlewares  []string
	Security     []string
	Comment      string
	// 新增字段：模块和目录信息
	FilePath     string `json:"file_path"`     // 文件路径
	Directory    string `json:"directory"`     // 所在目录
	Module       string `json:"module"`        // 所属模块
	RelativePath string `json:"relative_path"` // 相对路径
}

// MiddlewareDefinition 中间件定义信息
type MiddlewareDefinition struct {
	Name        string
	Type        string
	Config      map[string]interface{}
	Description string
}

// NewASTParser 创建新的AST解析器
func NewASTParser() *ASTParser {
	return &ASTParser{
		FileSet:     token.NewFileSet(),
		Files:       make(map[string]*ast.File),
		TypeInfo:    make(map[string]*TypeDefinition),
		Groups:      make([]*GroupDefinition, 0),
		Endpoints:   make([]*EndpointDefinition, 0),
		Middlewares: make(map[string]*MiddlewareDefinition),
		Modules:     make(map[string]*ModuleDefinition),
	}
}

// ParseFile 解析单个Go文件
func (p *ASTParser) ParseFile(filename string) error {
	file, err := parser.ParseFile(p.FileSet, filename, nil, parser.ParseComments)
	if err != nil {
		return fmt.Errorf("解析文件 %s 失败: %v", filename, err)
	}

	p.Files[filename] = file

	// 解析文件内容
	if err := p.analyzeFile(file); err != nil {
		return fmt.Errorf("分析文件 %s 失败: %v", filename, err)
	}

	return nil
}

// ParseDirectory 解析目录中的所有Go文件
func (p *ASTParser) ParseDirectory(dir string) error {
	// 使用新的递归解析方法
	return p.ParseDirectoryRecursive(dir)
}

// ParseDirectoryNonRecursive 非递归地解析单个目录
func (p *ASTParser) ParseDirectoryNonRecursive(dir string) error {
	pkgs, err := parser.ParseDir(p.FileSet, dir, nil, parser.ParseComments)
	if err != nil {
		return fmt.Errorf("解析目录 %s 失败: %v", dir, err)
	}

	for pkgName, pkg := range pkgs {
		log.Printf("解析包: %s", pkgName)
		for filename, file := range pkg.Files {
			p.Files[filename] = file
			if err := p.analyzeFile(file); err != nil {
				return fmt.Errorf("分析文件 %s 失败: %v", filename, err)
			}
		}
	}

	return nil
}

// analyzeFile 分析AST文件
func (p *ASTParser) analyzeFile(file *ast.File) error {
	// 遍历文件中的所有声明
	for _, decl := range file.Decls {
		switch d := decl.(type) {
		case *ast.GenDecl: // 通用声明
			if err := p.analyzeGenDecl(d); err != nil {
				return err
			}
		case *ast.FuncDecl: // 函数声明
			if err := p.analyzeFuncDecl(d); err != nil {
				return err
			}
		}
	}

	return nil
}

// analyzeGenDecl 分析通用声明（类型、变量、常量）
func (p *ASTParser) analyzeGenDecl(decl *ast.GenDecl) error {
	switch decl.Tok {
	case token.TYPE:
		// 分析类型声明
		for _, spec := range decl.Specs {
			if typeSpec, ok := spec.(*ast.TypeSpec); ok {
				if err := p.analyzeTypeSpec(typeSpec, decl.Doc); err != nil {
					return err
				}
			}
		}
	case token.VAR:
		// 分析变量声明（可能包含API定义）
		for _, spec := range decl.Specs {
			if valueSpec, ok := spec.(*ast.ValueSpec); ok {
				if err := p.analyzeVarSpec(valueSpec, decl.Doc); err != nil {
					return err
				}
			}
		}
	}

	return nil
}

// analyzeTypeSpec 分析类型规范
func (p *ASTParser) analyzeTypeSpec(spec *ast.TypeSpec, doc *ast.CommentGroup) error {
	typeDef := &TypeDefinition{
		Name:    spec.Name.Name,
		Fields:  make([]*FieldDefinition, 0),
		Methods: make([]*MethodDefinition, 0),
		Tags:    make(map[string]string),
	}

	if doc != nil {
		typeDef.Comment = doc.Text()
	}

	switch typeExpr := spec.Type.(type) {
	case *ast.StructType:
		typeDef.IsStruct = true
		if err := p.analyzeStructType(typeExpr, typeDef); err != nil {
			return err
		}
	case *ast.InterfaceType:
		typeDef.IsInterface = true
		if err := p.analyzeInterfaceType(typeExpr, typeDef); err != nil {
			return err
		}
	}

	p.TypeInfo[typeDef.Name] = typeDef
	return nil
}

// analyzeStructType 分析结构体类型
func (p *ASTParser) analyzeStructType(structType *ast.StructType, typeDef *TypeDefinition) error {
	for _, field := range structType.Fields.List {
		fieldDef := &FieldDefinition{}

		// 字段名
		if len(field.Names) > 0 {
			fieldDef.Name = field.Names[0].Name
		} else {
			// 匿名字段
			fieldDef.Name = p.getTypeString(field.Type)
		}

		// 字段类型
		fieldDef.Type = p.getTypeString(field.Type)

		// 字段标签
		if field.Tag != nil {
			tagValue := field.Tag.Value
			tagValue = strings.Trim(tagValue, "`")
			p.parseFieldTags(tagValue, fieldDef)
		}

		// 字段注释
		if field.Doc != nil {
			fieldDef.Comment = field.Doc.Text()
		} else if field.Comment != nil {
			fieldDef.Comment = field.Comment.Text()
		}

		typeDef.Fields = append(typeDef.Fields, fieldDef)
	}

	return nil
}

// analyzeInterfaceType 分析接口类型
func (p *ASTParser) analyzeInterfaceType(interfaceType *ast.InterfaceType, typeDef *TypeDefinition) error {
	for _, method := range interfaceType.Methods.List {
		if len(method.Names) > 0 {
			methodDef := &MethodDefinition{
				Name:       method.Names[0].Name,
				Parameters: make([]*ParameterDefinition, 0),
				Results:    make([]*ParameterDefinition, 0),
			}

			if funcType, ok := method.Type.(*ast.FuncType); ok {
				// 分析参数
				if funcType.Params != nil {
					for _, param := range funcType.Params.List {
						paramDef := &ParameterDefinition{
							Type: p.getTypeString(param.Type),
						}
						if len(param.Names) > 0 {
							paramDef.Name = param.Names[0].Name
						}
						methodDef.Parameters = append(methodDef.Parameters, paramDef)
					}
				}

				// 分析返回值
				if funcType.Results != nil {
					for _, result := range funcType.Results.List {
						resultDef := &ParameterDefinition{
							Type: p.getTypeString(result.Type),
						}
						if len(result.Names) > 0 {
							resultDef.Name = result.Names[0].Name
						}
						methodDef.Results = append(methodDef.Results, resultDef)
					}
				}
			}

			if method.Doc != nil {
				methodDef.Comment = method.Doc.Text()
			}

			typeDef.Methods = append(typeDef.Methods, methodDef)
		}
	}

	return nil
}

// analyzeVarSpec 分析变量规范（查找API定义）
func (p *ASTParser) analyzeVarSpec(spec *ast.ValueSpec, doc *ast.CommentGroup) error {
	// 这里可以分析变量声明中的API定义
	// 例如: var userAPI = defineUserAPI()
	for _, name := range spec.Names {
		if strings.Contains(strings.ToLower(name.Name), "api") {
			log.Printf("发现可能的API定义变量: %s", name.Name)
		}
	}

	return nil
}

// analyzeFuncDecl 分析函数声明
func (p *ASTParser) analyzeFuncDecl(decl *ast.FuncDecl) error {
	funcName := decl.Name.Name

	// 检查是否是API定义函数
	if strings.Contains(strings.ToLower(funcName), "api") ||
		strings.Contains(strings.ToLower(funcName), "route") ||
		strings.Contains(strings.ToLower(funcName), "handler") {

		log.Printf("发现可能的API相关函数: %s", funcName)

		// 分析函数体以提取API定义
		if decl.Body != nil {
			if err := p.analyzeFuncBody(decl.Body, funcName); err != nil {
				return err
			}
		}
	}

	return nil
}

// analyzeFuncBody 分析函数体
func (p *ASTParser) analyzeFuncBody(body *ast.BlockStmt, funcName string) error {
	for _, stmt := range body.List {
		switch s := stmt.(type) {
		case *ast.AssignStmt:
			if err := p.analyzeAssignStmt(s); err != nil {
				return err
			}
		case *ast.ExprStmt:
			if err := p.analyzeExprStmt(s); err != nil {
				return err
			}
		}
	}

	return nil
}

// analyzeAssignStmt 分析赋值语句
func (p *ASTParser) analyzeAssignStmt(stmt *ast.AssignStmt) error {
	// 分析赋值语句中的API定义
	for i, rhs := range stmt.Rhs {
		if i < len(stmt.Lhs) {
			if ident, ok := stmt.Lhs[i].(*ast.Ident); ok {
				varName := ident.Name

				// 检查是否是NewGroup调用
				if callExpr, ok := rhs.(*ast.CallExpr); ok {
					if err := p.analyzeCallExpr(callExpr, varName); err != nil {
						return err
					}
				}
			}
		}
	}

	return nil
}

// analyzeExprStmt 分析表达式语句
func (p *ASTParser) analyzeExprStmt(stmt *ast.ExprStmt) error {
	if callExpr, ok := stmt.X.(*ast.CallExpr); ok {
		return p.analyzeCallExpr(callExpr, "")
	}
	return nil
}

// analyzeCallExpr 分析函数调用表达式
func (p *ASTParser) analyzeCallExpr(expr *ast.CallExpr, varName string) error {
	// 获取函数名
	funcName := p.getFunctionName(expr.Fun)

	switch funcName {
	case "NewGroup":
		return p.parseNewGroup(expr, varName)
	case "NewAPI":
		return p.parseNewAPI(expr, varName)
	case "GET", "POST", "PUT", "DELETE", "PATCH":
		return p.parseHTTPMethod(expr, funcName)
	}

	// 检查链式调用
	if selectorExpr, ok := expr.Fun.(*ast.SelectorExpr); ok {
		methodName := selectorExpr.Sel.Name
		switch methodName {
		case "GET", "POST", "PUT", "DELETE", "PATCH":
			return p.parseHTTPMethod(expr, methodName)
		case "Handler", "Summary", "Description", "Tags", "Request", "Response", "Security":
			return p.parseEndpointBuilder(expr, methodName)
		case "WithTags", "WithMiddleware":
			return p.parseGroupBuilder(expr, methodName)
		}
	}

	return nil
}

// parseNewGroup 解析NewGroup调用
func (p *ASTParser) parseNewGroup(expr *ast.CallExpr, varName string) error {
	if len(expr.Args) > 0 {
		if basicLit, ok := expr.Args[0].(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
			prefix, _ := strconv.Unquote(basicLit.Value)

			groupDef := &GroupDefinition{
				Name:      varName,
				Prefix:    prefix,
				Tags:      make([]string, 0),
				Endpoints: make([]*EndpointDefinition, 0),
			}

			p.Groups = append(p.Groups, groupDef)
			log.Printf("解析到路由组: %s, 前缀: %s", varName, prefix)
		}
	}
	return nil
}

// parseNewAPI 解析NewAPI调用
func (p *ASTParser) parseNewAPI(expr *ast.CallExpr, varName string) error {
	// 解析API信息
	log.Printf("解析到API定义: %s", varName)
	return nil
}

// parseHTTPMethod 解析HTTP方法调用
func (p *ASTParser) parseHTTPMethod(expr *ast.CallExpr, method string) error {
	if len(expr.Args) > 0 {
		if basicLit, ok := expr.Args[0].(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
			path, _ := strconv.Unquote(basicLit.Value)

			endpointDef := &EndpointDefinition{
				Method: method,
				Path:   path,
			}

			p.Endpoints = append(p.Endpoints, endpointDef)
			log.Printf("解析到端点: %s %s", method, path)
		}
	}
	return nil
}

// parseEndpointBuilder 解析端点构建器方法
func (p *ASTParser) parseEndpointBuilder(expr *ast.CallExpr, methodName string) error {
	if len(p.Endpoints) > 0 {
		endpoint := p.Endpoints[len(p.Endpoints)-1]

		if len(expr.Args) > 0 {
			switch methodName {
			case "Handler":
				if basicLit, ok := expr.Args[0].(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
					handler, _ := strconv.Unquote(basicLit.Value)
					endpoint.Handler = handler
				}
			case "Summary":
				if basicLit, ok := expr.Args[0].(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
					summary, _ := strconv.Unquote(basicLit.Value)
					endpoint.Summary = summary
				}
			case "Description":
				if basicLit, ok := expr.Args[0].(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
					description, _ := strconv.Unquote(basicLit.Value)
					endpoint.Description = description
				}
			}
		}
	}
	return nil
}

// parseGroupBuilder 解析路由组构建器方法
func (p *ASTParser) parseGroupBuilder(expr *ast.CallExpr, methodName string) error {
	if len(p.Groups) > 0 {
		group := p.Groups[len(p.Groups)-1]

		switch methodName {
		case "WithTags":
			for _, arg := range expr.Args {
				if basicLit, ok := arg.(*ast.BasicLit); ok && basicLit.Kind == token.STRING {
					tag, _ := strconv.Unquote(basicLit.Value)
					group.Tags = append(group.Tags, tag)
				}
			}
		}
	}
	return nil
}

// getTypeString 获取类型的字符串表示
func (p *ASTParser) getTypeString(expr ast.Expr) string {
	switch t := expr.(type) {
	case *ast.Ident:
		return t.Name
	case *ast.SelectorExpr:
		return p.getTypeString(t.X) + "." + t.Sel.Name
	case *ast.StarExpr:
		return "*" + p.getTypeString(t.X)
	case *ast.ArrayType:
		return "[]" + p.getTypeString(t.Elt)
	case *ast.MapType:
		return "map[" + p.getTypeString(t.Key) + "]" + p.getTypeString(t.Value)
	case *ast.InterfaceType:
		return "interface{}"
	default:
		return "unknown"
	}
}

// getFunctionName 获取函数名
func (p *ASTParser) getFunctionName(expr ast.Expr) string {
	switch e := expr.(type) {
	case *ast.Ident:
		return e.Name
	case *ast.SelectorExpr:
		return e.Sel.Name
	default:
		return ""
	}
}

// parseFieldTags 解析字段标签
func (p *ASTParser) parseFieldTags(tagStr string, field *FieldDefinition) {
	// 解析结构体标签
	tag := reflect.StructTag(tagStr)

	if jsonTag := tag.Get("json"); jsonTag != "" {
		field.JSONTag = jsonTag
		if strings.Contains(jsonTag, "omitempty") {
			field.Optional = true
		}
	}

	if formTag := tag.Get("form"); formTag != "" {
		field.FormTag = formTag
	}

	if validateTag := tag.Get("validate"); validateTag != "" {
		field.ValidTag = validateTag
	}
}

// GetParsedAPI 获取解析后的API定义
func (p *ASTParser) GetParsedAPI() *API {
	if p.APIInfo == nil {
		p.APIInfo = &APIInfo{
			Title:   "Generated API",
			Version: "v1.0.0",
		}
	}

	api := &API{
		Info:        *p.APIInfo,
		Groups:      make([]Group, 0),
		Endpoints:   make([]Endpoint, 0),
		Middlewares: make(map[string]MiddlewareFunc),
	}

	// 转换Groups
	for _, groupDef := range p.Groups {
		group := Group{
			Prefix: groupDef.Prefix,
			Tags:   groupDef.Tags,
		}
		api.Groups = append(api.Groups, group)
	}

	// 转换Endpoints
	for _, endpointDef := range p.Endpoints {
		endpoint := Endpoint{
			Method:      HTTPMethod(endpointDef.Method),
			Path:        endpointDef.Path,
			Handler:     endpointDef.Handler,
			Summary:     endpointDef.Summary,
			Description: endpointDef.Description,
			Tags:        endpointDef.Tags,
		}
		api.Endpoints = append(api.Endpoints, endpoint)
	}

	return api
}

// PrintParseResults 打印解析结果
func (p *ASTParser) PrintParseResults() {
	fmt.Println("=== AST 解析结果 ===")

	fmt.Printf("类型定义数量: %d\n", len(p.TypeInfo))
	for name, typeDef := range p.TypeInfo {
		fmt.Printf("  类型: %s (字段数: %d)\n", name, len(typeDef.Fields))
	}

	fmt.Printf("路由组数量: %d\n", len(p.Groups))
	for _, group := range p.Groups {
		fmt.Printf("  路由组: %s -> %s\n", group.Name, group.Prefix)
	}

	fmt.Printf("端点数量: %d\n", len(p.Endpoints))
	for _, endpoint := range p.Endpoints {
		fmt.Printf("  端点: %s %s -> %s\n", endpoint.Method, endpoint.Path, endpoint.Handler)
	}

	fmt.Printf("模块数量: %d\n", len(p.Modules))
	for name, module := range p.Modules {
		fmt.Printf("  模块: %s -> %s\n", name, module.Path)
	}
}

// ParseDirectoryRecursive 递归解析目录结构
func (p *ASTParser) ParseDirectoryRecursive(rootDir string) error {
	p.RootPath = rootDir
	return p.parseDirectoryWithModule(rootDir, "")
}

// parseDirectoryWithModule 解析目录并识别模块
func (p *ASTParser) parseDirectoryWithModule(dir string, parentModule string) error {
	// 计算相对路径和模块名
	relPath, err := filepath.Rel(p.RootPath, dir)
	if err != nil {
		return err
	}

	moduleName := p.inferModuleName(relPath)
	if parentModule != "" {
		moduleName = parentModule + "/" + moduleName
	}

	p.CurrentModule = moduleName

	// 创建模块定义
	module := &ModuleDefinition{
		Name:        filepath.Base(dir),
		Path:        dir,
		Description: fmt.Sprintf("功能模块: %s", moduleName),
		Package:     p.inferPackageName(dir),
		Types:       make([]*TypeDefinition, 0),
		Groups:      make([]*GroupDefinition, 0),
		Endpoints:   make([]*EndpointDefinition, 0),
		SubModules:  make([]*ModuleDefinition, 0),
		Files:       make([]string, 0),
		DependsOn:   make([]string, 0),
	}

	// 解析当前目录中的Go文件
	pkgs, err := parser.ParseDir(p.FileSet, dir, nil, parser.ParseComments)
	if err != nil {
		return fmt.Errorf("解析目录 %s 失败: %v", dir, err)
	}

	for pkgName, pkg := range pkgs {
		log.Printf("解析包: %s (模块: %s)", pkgName, moduleName)
		for filename, file := range pkg.Files {
			p.Files[filename] = file
			module.Files = append(module.Files, filename)

			// 设置文件信息到解析对象中
			if err := p.analyzeFileWithModule(file, filename, moduleName); err != nil {
				return fmt.Errorf("分析文件 %s 失败: %v", filename, err)
			}
		}
	}

	// 收集模块中的类型、组和端点
	p.collectModuleElements(module)

	// 存储模块
	p.Modules[moduleName] = module

	// 递归处理子目录
	return p.processSubDirectories(dir, moduleName)
}

// analyzeFileWithModule 分析文件并设置模块信息
func (p *ASTParser) analyzeFileWithModule(file *ast.File, filename string, moduleName string) error {
	dir := filepath.Dir(filename)
	relPath, _ := filepath.Rel(p.RootPath, filename)

	// 先进行正常的文件分析
	if err := p.analyzeFile(file); err != nil {
		return err
	}

	// 为所有新解析的元素设置模块信息
	p.setModuleInfoForElements(filename, dir, moduleName, relPath)

	return nil
}

// setModuleInfoForElements 为解析元素设置模块信息
func (p *ASTParser) setModuleInfoForElements(filePath, directory, module, relativePath string) {
	// 为类型定义设置模块信息
	for _, typeDef := range p.TypeInfo {
		if typeDef.FilePath == "" { // 只设置新解析的
			typeDef.FilePath = filePath
			typeDef.Directory = directory
			typeDef.Module = module
			typeDef.RelativePath = relativePath
		}
	}

	// 为路由组设置模块信息
	for _, group := range p.Groups {
		if group.FilePath == "" {
			group.FilePath = filePath
			group.Directory = directory
			group.Module = module
			group.RelativePath = relativePath
		}
	}

	// 为端点设置模块信息
	for _, endpoint := range p.Endpoints {
		if endpoint.FilePath == "" {
			endpoint.FilePath = filePath
			endpoint.Directory = directory
			endpoint.Module = module
			endpoint.RelativePath = relativePath
		}
	}
}

// collectModuleElements 收集模块中的元素
func (p *ASTParser) collectModuleElements(module *ModuleDefinition) {
	// 收集类型
	for _, typeDef := range p.TypeInfo {
		if typeDef.Module == module.Name || strings.HasPrefix(typeDef.Module, module.Name+"/") {
			module.Types = append(module.Types, typeDef)
		}
	}

	// 收集路由组
	for _, group := range p.Groups {
		if group.Module == module.Name || strings.HasPrefix(group.Module, module.Name+"/") {
			module.Groups = append(module.Groups, group)
		}
	}

	// 收集端点
	for _, endpoint := range p.Endpoints {
		if endpoint.Module == module.Name || strings.HasPrefix(endpoint.Module, module.Name+"/") {
			module.Endpoints = append(module.Endpoints, endpoint)
		}
	}
}

// processSubDirectories 处理子目录
func (p *ASTParser) processSubDirectories(dir string, parentModule string) error {
	entries, err := os.ReadDir(dir)
	if err != nil {
		return err
	}

	for _, entry := range entries {
		if entry.IsDir() {
			// 跳过隐藏目录和特殊目录
			name := entry.Name()
			if strings.HasPrefix(name, ".") ||
				name == "vendor" ||
				name == "node_modules" {
				continue
			}

			subDir := filepath.Join(dir, name)
			// 递归处理子目录
			if err := p.parseDirectoryWithModule(subDir, parentModule); err != nil {
				log.Printf("警告: 处理子目录 %s 失败: %v", subDir, err)
			}
		}
	}

	return nil
}

// inferModuleName 推断模块名称
func (p *ASTParser) inferModuleName(relPath string) string {
	if relPath == "." || relPath == "" {
		return "root"
	}
	return strings.ReplaceAll(relPath, string(filepath.Separator), "/")
}

// inferPackageName 推断包名
func (p *ASTParser) inferPackageName(dir string) string {
	baseName := filepath.Base(dir)
	// 简单的包名推断逻辑
	if baseName == "." {
		return "main"
	}
	return baseName
}

// GetModuleByPath 根据路径获取模块
func (p *ASTParser) GetModuleByPath(path string) *ModuleDefinition {
	for _, module := range p.Modules {
		if module.Path == path {
			return module
		}
	}
	return nil
}

// GetModulesByTag 根据标签获取模块
func (p *ASTParser) GetModulesByTag(tag string) []*ModuleDefinition {
	var modules []*ModuleDefinition
	for _, module := range p.Modules {
		// 检查模块中的组或端点是否包含指定标签
		for _, group := range module.Groups {
			for _, groupTag := range group.Tags {
				if groupTag == tag {
					modules = append(modules, module)
					goto nextModule
				}
			}
		}
		for _, endpoint := range module.Endpoints {
			for _, endpointTag := range endpoint.Tags {
				if endpointTag == tag {
					modules = append(modules, module)
					goto nextModule
				}
			}
		}
	nextModule:
	}
	return modules
}

// GetModuleHierarchy 获取模块层次结构
func (p *ASTParser) GetModuleHierarchy() map[string][]*ModuleDefinition {
	hierarchy := make(map[string][]*ModuleDefinition)

	for _, module := range p.Modules {
		// 计算模块层次
		parts := strings.Split(module.Name, "/")
		if len(parts) == 1 {
			// 顶级模块
			hierarchy["root"] = append(hierarchy["root"], module)
		} else {
			// 子模块
			parent := strings.Join(parts[:len(parts)-1], "/")
			hierarchy[parent] = append(hierarchy[parent], module)
		}
	}

	return hierarchy
}
