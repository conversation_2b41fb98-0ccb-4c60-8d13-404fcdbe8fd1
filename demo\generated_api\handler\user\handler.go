package user

import (
	"generated_api/logic/user"
	"github.com/gin-gonic/gin"
	"net/http"
)

// ListUsers 获取用户列表
func ListUsers(c *gin.Context) {
	// 调用logic层处理业务逻辑
	result, err := user.ListUsers()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, result)
}

// GetUser 获取用户详情
func GetUser(c *gin.Context) {
	// 调用logic层处理业务逻辑
	result, err := user.GetUser()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSO<PERSON>(http.StatusOK, result)
}

// CreateUser 创建用户
func CreateUser(c *gin.Context) {
	// 调用logic层处理业务逻辑
	result, err := user.CreateUser()
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.<PERSON>(http.StatusOK, result)
}

// UpdateUser 更新用户
func UpdateUser(c *gin.Context) {
	// 调用logic层处理业务逻辑
	result, err := user.UpdateUser()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, result)
}

// DeleteUser 删除用户
func DeleteUser(c *gin.Context) {
	// 调用logic层处理业务逻辑
	result, err := user.DeleteUser()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, result)
}
