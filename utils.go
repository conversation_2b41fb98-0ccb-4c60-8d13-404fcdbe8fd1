package newapi

import (
	"bufio"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"reflect"
	"strings"
)

// Utils 工具函数集合
type Utils struct{}

// NewUtils 创建工具实例
func NewUtils() *Utils {
	return &Utils{}
}

// GenerateSwagger 生成Swagger文档
func (u *Utils) GenerateSwagger(api *API) (map[string]interface{}, error) {
	swagger := map[string]interface{}{
		"swagger": "2.0",
		"info": map[string]interface{}{
			"title":       api.Info.Title,
			"description": api.Info.Description,
			"version":     api.Info.Version,
			"contact": map[string]interface{}{
				"name":  api.Info.Author,
				"email": api.Info.Email,
			},
		},
		"host":                api.Info.Host,
		"basePath":            api.Info.BasePath,
		"schemes":             []string{"http", "https"},
		"consumes":            []string{"application/json"},
		"produces":            []string{"application/json"},
		"paths":               make(map[string]interface{}),
		"definitions":         make(map[string]interface{}),
		"securityDefinitions": make(map[string]interface{}),
	}

	// 添加安全定义
	for name, security := range api.Security {
		swagger["securityDefinitions"].(map[string]interface{})[name] = map[string]interface{}{
			"type":        security.Type,
			"description": security.Description,
			"name":        security.Name,
			"in":          security.In,
		}
	}

	// 添加路径定义
	paths := swagger["paths"].(map[string]interface{})
	definitions := swagger["definitions"].(map[string]interface{})

	// 处理路由组
	for _, group := range api.Groups {
		u.addGroupToSwagger(group, paths, definitions, api.Info.BasePath)
	}

	// 处理独立端点
	for _, endpoint := range api.Endpoints {
		u.addEndpointToSwagger(endpoint, paths, definitions, api.Info.BasePath)
	}

	return swagger, nil
}

// addGroupToSwagger 将路由组添加到Swagger文档
func (u *Utils) addGroupToSwagger(group Group, paths, definitions map[string]interface{}, basePath string) {
	for _, endpoint := range group.Endpoints {
		fullPath := basePath + group.Prefix + endpoint.Path
		u.addEndpointToSwagger(endpoint, paths, definitions, fullPath)
	}

	// 递归处理子组
	for _, subGroup := range group.SubGroups {
		u.addGroupToSwagger(subGroup, paths, definitions, basePath+group.Prefix)
	}
}

// addEndpointToSwagger 将端点添加到Swagger文档
func (u *Utils) addEndpointToSwagger(endpoint Endpoint, paths, definitions map[string]interface{}, fullPath string) {
	method := strings.ToLower(string(endpoint.Method))

	if paths[fullPath] == nil {
		paths[fullPath] = make(map[string]interface{})
	}

	operation := map[string]interface{}{
		"summary":     endpoint.Summary,
		"description": endpoint.Description,
		"tags":        endpoint.Tags,
		"operationId": endpoint.Handler,
		"responses": map[string]interface{}{
			"200": map[string]interface{}{
				"description": "Success",
			},
		},
	}

	// 添加请求参数
	if endpoint.RequestType != nil {
		u.addTypeToDefinitions(endpoint.RequestType, definitions)
		typeName := GetTypeName(endpoint.RequestType)

		if method == "get" || method == "delete" {
			// GET和DELETE请求使用query参数
			operation["parameters"] = u.generateQueryParameters(endpoint.RequestType)
		} else {
			// POST、PUT、PATCH请求使用body参数
			operation["parameters"] = []map[string]interface{}{
				{
					"name":        "body",
					"in":          "body",
					"description": "Request body",
					"required":    true,
					"schema": map[string]interface{}{
						"$ref": fmt.Sprintf("#/definitions/%s", typeName),
					},
				},
			}
		}
	}

	// 添加响应类型
	if endpoint.ResponseType != nil {
		u.addTypeToDefinitions(endpoint.ResponseType, definitions)
		typeName := GetTypeName(endpoint.ResponseType)
		operation["responses"].(map[string]interface{})["200"].(map[string]interface{})["schema"] = map[string]interface{}{
			"$ref": fmt.Sprintf("#/definitions/%s", typeName),
		}
	}

	// 添加安全要求
	if len(endpoint.Security) > 0 {
		security := make([]map[string][]string, 0)
		for _, sec := range endpoint.Security {
			security = append(security, map[string][]string{sec: {}})
		}
		operation["security"] = security
	}

	paths[fullPath].(map[string]interface{})[method] = operation
}

// addTypeToDefinitions 将类型添加到定义中
func (u *Utils) addTypeToDefinitions(t interface{}, definitions map[string]interface{}) {
	typeName := GetTypeName(t)
	if typeName == "" || definitions[typeName] != nil {
		return
	}

	typ := reflect.TypeOf(t)
	if typ.Kind() == reflect.Ptr {
		typ = typ.Elem()
	}

	if typ.Kind() != reflect.Struct {
		return
	}

	properties := make(map[string]interface{})
	required := make([]string, 0)

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		jsonTag := field.Tag.Get("json")

		// 跳过忽略的字段
		if jsonTag == "-" {
			continue
		}

		fieldName := field.Name
		if jsonTag != "" {
			parts := strings.Split(jsonTag, ",")
			if parts[0] != "" {
				fieldName = parts[0]
			}
		}

		property := map[string]interface{}{
			"type": u.getSwaggerType(field.Type),
		}

		// 检查是否必填
		validateTag := field.Tag.Get("validate")
		if strings.Contains(validateTag, "required") {
			required = append(required, fieldName)
		}

		properties[fieldName] = property
	}

	definition := map[string]interface{}{
		"type":       "object",
		"properties": properties,
	}

	if len(required) > 0 {
		definition["required"] = required
	}

	definitions[typeName] = definition
}

// getSwaggerType 获取Swagger类型
func (u *Utils) getSwaggerType(t reflect.Type) string {
	switch t.Kind() {
	case reflect.String:
		return "string"
	case reflect.Bool:
		return "boolean"
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		return "integer"
	case reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64:
		return "integer"
	case reflect.Float32, reflect.Float64:
		return "number"
	case reflect.Slice, reflect.Array:
		return "array"
	case reflect.Map, reflect.Struct:
		return "object"
	case reflect.Ptr:
		return u.getSwaggerType(t.Elem())
	default:
		return "string"
	}
}

// generateQueryParameters 生成查询参数
func (u *Utils) generateQueryParameters(t interface{}) []map[string]interface{} {
	typ := reflect.TypeOf(t)
	if typ.Kind() == reflect.Ptr {
		typ = typ.Elem()
	}

	if typ.Kind() != reflect.Struct {
		return nil
	}

	parameters := make([]map[string]interface{}, 0)

	for i := 0; i < typ.NumField(); i++ {
		field := typ.Field(i)
		formTag := field.Tag.Get("form")

		if formTag == "" || formTag == "-" {
			continue
		}

		fieldName := formTag
		if strings.Contains(formTag, ",") {
			parts := strings.Split(formTag, ",")
			fieldName = parts[0]
		}

		parameter := map[string]interface{}{
			"name":        fieldName,
			"in":          "query",
			"type":        u.getSwaggerType(field.Type),
			"description": field.Name,
		}

		// 检查是否必填
		validateTag := field.Tag.Get("validate")
		if strings.Contains(validateTag, "required") {
			parameter["required"] = true
		}

		parameters = append(parameters, parameter)
	}

	return parameters
}

// ExportToJSON 导出API定义为JSON
func (u *Utils) ExportToJSON(api *API) ([]byte, error) {
	data := map[string]interface{}{
		"info":        api.Info,
		"groups":      api.Groups,
		"endpoints":   api.Endpoints,
		"middlewares": u.serializeMiddlewares(api.Middlewares),
		"security":    api.Security,
	}

	return json.MarshalIndent(data, "", "  ")
}

// serializeMiddlewares 序列化中间件
func (u *Utils) serializeMiddlewares(middlewares map[string]MiddlewareFunc) map[string]string {
	result := make(map[string]string)
	for name := range middlewares {
		result[name] = fmt.Sprintf("MiddlewareFunc<%s>", name)
	}
	return result
}

// ValidateEndpoint 验证单个端点
func (u *Utils) ValidateEndpoint(endpoint Endpoint) []string {
	var errors []string

	if endpoint.Method == "" {
		errors = append(errors, "端点方法不能为空")
	}

	if endpoint.Path == "" {
		errors = append(errors, "端点路径不能为空")
	}

	if endpoint.Handler == "" {
		errors = append(errors, "端点处理器不能为空")
	}

	// 验证路径格式
	if endpoint.Path != "" && !strings.HasPrefix(endpoint.Path, "/") {
		errors = append(errors, "端点路径必须以'/'开头")
	}

	// 验证HTTP方法
	validMethods := map[HTTPMethod]bool{
		GET: true, POST: true, PUT: true, DELETE: true,
		PATCH: true, HEAD: true, OPTIONS: true,
	}
	if !validMethods[endpoint.Method] {
		errors = append(errors, fmt.Sprintf("无效的HTTP方法: %s", endpoint.Method))
	}

	return errors
}

// GenerateAPIDoc 生成API文档
func (u *Utils) GenerateAPIDoc(api *API) string {
	var doc strings.Builder

	doc.WriteString(fmt.Sprintf("# %s\n\n", api.Info.Title))
	doc.WriteString(fmt.Sprintf("**版本**: %s\n", api.Info.Version))
	doc.WriteString(fmt.Sprintf("**描述**: %s\n", api.Info.Description))
	doc.WriteString(fmt.Sprintf("**作者**: %s\n", api.Info.Author))
	if api.Info.Email != "" {
		doc.WriteString(fmt.Sprintf("**联系邮箱**: %s\n", api.Info.Email))
	}
	doc.WriteString("\n")

	// 添加路由组文档
	if len(api.Groups) > 0 {
		doc.WriteString("## 路由组\n\n")
		for _, group := range api.Groups {
			doc.WriteString(fmt.Sprintf("### %s\n\n", group.Prefix))
			doc.WriteString(fmt.Sprintf("**前缀**: `%s`\n", group.Prefix))
			if len(group.Tags) > 0 {
				doc.WriteString(fmt.Sprintf("**标签**: %s\n", strings.Join(group.Tags, ", ")))
			}
			doc.WriteString("\n")

			for _, endpoint := range group.Endpoints {
				doc.WriteString(fmt.Sprintf("#### %s %s%s\n\n", endpoint.Method, group.Prefix, endpoint.Path))
				if endpoint.Summary != "" {
					doc.WriteString(fmt.Sprintf("**摘要**: %s\n", endpoint.Summary))
				}
				if endpoint.Description != "" {
					doc.WriteString(fmt.Sprintf("**描述**: %s\n", endpoint.Description))
				}
				doc.WriteString(fmt.Sprintf("**处理器**: `%s`\n", endpoint.Handler))
				if len(endpoint.Tags) > 0 {
					doc.WriteString(fmt.Sprintf("**标签**: %s\n", strings.Join(endpoint.Tags, ", ")))
				}
				doc.WriteString("\n")
			}
		}
	}

	// 添加独立端点文档
	if len(api.Endpoints) > 0 {
		doc.WriteString("## 独立端点\n\n")
		for _, endpoint := range api.Endpoints {
			doc.WriteString(fmt.Sprintf("### %s %s\n\n", endpoint.Method, endpoint.Path))
			if endpoint.Summary != "" {
				doc.WriteString(fmt.Sprintf("**摘要**: %s\n", endpoint.Summary))
			}
			if endpoint.Description != "" {
				doc.WriteString(fmt.Sprintf("**描述**: %s\n", endpoint.Description))
			}
			doc.WriteString(fmt.Sprintf("**处理器**: `%s`\n", endpoint.Handler))
			if len(endpoint.Tags) > 0 {
				doc.WriteString(fmt.Sprintf("**标签**: %s\n", strings.Join(endpoint.Tags, ", ")))
			}
			doc.WriteString("\n")
		}
	}

	return doc.String()
}

// PrintAPIInfo 打印API信息
func (u *Utils) PrintAPIInfo(api *API) {
	fmt.Printf("=== API信息 ===\n")
	fmt.Printf("标题: %s\n", api.Info.Title)
	fmt.Printf("版本: %s\n", api.Info.Version)
	fmt.Printf("描述: %s\n", api.Info.Description)
	fmt.Printf("作者: %s\n", api.Info.Author)
	fmt.Printf("邮箱: %s\n", api.Info.Email)
	fmt.Printf("主机: %s\n", api.Info.Host)
	fmt.Printf("基础路径: %s\n", api.Info.BasePath)
	fmt.Printf("\n")

	fmt.Printf("=== 统计信息 ===\n")
	fmt.Printf("路由组数量: %d\n", len(api.Groups))
	fmt.Printf("独立端点数量: %d\n", len(api.Endpoints))
	fmt.Printf("中间件数量: %d\n", len(api.Middlewares))
	fmt.Printf("安全定义数量: %d\n", len(api.Security))
	fmt.Printf("\n")

	totalEndpoints := len(api.Endpoints)
	for _, group := range api.Groups {
		totalEndpoints += len(group.Endpoints)
	}
	fmt.Printf("总端点数量: %d\n", totalEndpoints)
}

// GenerateModularAPIDoc 生成模块化API文档
func (u *Utils) GenerateModularAPIDoc(modules map[string]*ModuleDefinition) string {
	var doc strings.Builder

	doc.WriteString("# 模块化API文档\n\n")
	doc.WriteString("此文档按模块组织，显示了项目的目录结构和功能分布。\n\n")

	// 按模块层级组织
	rootModules := make([]*ModuleDefinition, 0)
	subModules := make(map[string][]*ModuleDefinition)

	for _, module := range modules {
		parts := strings.Split(module.Name, "/")
		if len(parts) == 1 {
			rootModules = append(rootModules, module)
		} else {
			parent := strings.Join(parts[:len(parts)-1], "/")
			subModules[parent] = append(subModules[parent], module)
		}
	}

	// 生成模块文档
	u.generateModuleDocRecursive(&doc, rootModules, subModules, 0)

	return doc.String()
}

// generateModuleDocRecursive 递归生成模块文档
func (u *Utils) generateModuleDocRecursive(doc *strings.Builder, modules []*ModuleDefinition,
	subModules map[string][]*ModuleDefinition, level int) {

	for _, module := range modules {
		// 模块标题
		headerPrefix := strings.Repeat("#", level+2)
		indentPrefix := strings.Repeat("  ", level)

		doc.WriteString(fmt.Sprintf("%s %s\n\n", headerPrefix, module.Name))
		doc.WriteString(fmt.Sprintf("%s**路径**: `%s`\n", indentPrefix, module.Path))
		doc.WriteString(fmt.Sprintf("%s**描述**: %s\n", indentPrefix, module.Description))
		doc.WriteString(fmt.Sprintf("%s**包名**: %s\n\n", indentPrefix, module.Package))

		// 模块统计
		doc.WriteString(fmt.Sprintf("%s**统计信息**:\n", indentPrefix))
		doc.WriteString(fmt.Sprintf("%s- 类型数量: %d\n", indentPrefix, len(module.Types)))
		doc.WriteString(fmt.Sprintf("%s- 路由组数量: %d\n", indentPrefix, len(module.Groups)))
		doc.WriteString(fmt.Sprintf("%s- 端点数量: %d\n", indentPrefix, len(module.Endpoints)))
		doc.WriteString(fmt.Sprintf("%s- 文件数量: %d\n\n", indentPrefix, len(module.Files)))

		// 类型定义
		if len(module.Types) > 0 {
			doc.WriteString(fmt.Sprintf("%s### 类型定义\n\n", indentPrefix))
			for _, typeDef := range module.Types {
				doc.WriteString(fmt.Sprintf("%s- **%s**: ", indentPrefix, typeDef.Name))
				if typeDef.IsStruct {
					doc.WriteString(fmt.Sprintf("结构体 (%d个字段)", len(typeDef.Fields)))
				} else if typeDef.IsInterface {
					doc.WriteString("接口")
				} else {
					doc.WriteString("类型")
				}
				if typeDef.Comment != "" {
					doc.WriteString(fmt.Sprintf(" // %s", strings.TrimSpace(typeDef.Comment)))
				}
				doc.WriteString("\n")
			}
			doc.WriteString("\n")
		}

		// 路由组
		if len(module.Groups) > 0 {
			doc.WriteString(fmt.Sprintf("%s### 路由组\n\n", indentPrefix))
			for _, group := range module.Groups {
				doc.WriteString(fmt.Sprintf("%s- **%s** (%s): %d个端点\n",
					indentPrefix, group.Name, group.Prefix, len(group.Endpoints)))
				if len(group.Tags) > 0 {
					doc.WriteString(fmt.Sprintf("%s  - 标签: %s\n", indentPrefix, strings.Join(group.Tags, ", ")))
				}
			}
			doc.WriteString("\n")
		}

		// 独立端点
		if len(module.Endpoints) > 0 {
			doc.WriteString(fmt.Sprintf("%s### 独立端点\n\n", indentPrefix))
			for _, endpoint := range module.Endpoints {
				doc.WriteString(fmt.Sprintf("%s- **%s %s** -> %s",
					indentPrefix, endpoint.Method, endpoint.Path, endpoint.Handler))
				if endpoint.Summary != "" {
					doc.WriteString(fmt.Sprintf(" // %s", endpoint.Summary))
				}
				doc.WriteString("\n")
			}
			doc.WriteString("\n")
		}

		// 文件列表
		if len(module.Files) > 0 {
			doc.WriteString(fmt.Sprintf("%s### 文件列表\n\n", indentPrefix))
			for _, file := range module.Files {
				relFile := strings.TrimPrefix(file, module.Path)
				relFile = strings.TrimPrefix(relFile, string(filepath.Separator))
				doc.WriteString(fmt.Sprintf("%s- `%s`\n", indentPrefix, relFile))
			}
			doc.WriteString("\n")
		}

		// 递归处理子模块
		if children, exists := subModules[module.Name]; exists {
			u.generateModuleDocRecursive(doc, children, subModules, level+1)
		}
	}
}

// AnalyzeDirectoryStructure 分析目录结构
func (u *Utils) AnalyzeDirectoryStructure(rootPath string) (*DirectoryAnalysis, error) {
	analysis := &DirectoryAnalysis{
		RootPath:    rootPath,
		Modules:     make(map[string]*ModuleStats),
		FilesByType: make(map[string][]string),
		TotalFiles:  0,
		Depth:       0,
	}

	return analysis, u.analyzeDirectoryRecursive(rootPath, analysis, 0)
}

// DirectoryAnalysis 目录分析结果
type DirectoryAnalysis struct {
	RootPath    string                  `json:"root_path"`
	Modules     map[string]*ModuleStats `json:"modules"`
	FilesByType map[string][]string     `json:"files_by_type"`
	TotalFiles  int                     `json:"total_files"`
	Depth       int                     `json:"depth"`
}

// ModuleStats 模块统计信息
type ModuleStats struct {
	Path       string   `json:"path"`
	GoFiles    []string `json:"go_files"`
	TestFiles  []string `json:"test_files"`
	OtherFiles []string `json:"other_files"`
	FileCount  int      `json:"file_count"`
	LineCount  int      `json:"line_count"`
	SubDirs    []string `json:"sub_dirs"`
}

// analyzeDirectoryRecursive 递归分析目录
func (u *Utils) analyzeDirectoryRecursive(dirPath string, analysis *DirectoryAnalysis, depth int) error {
	if depth > analysis.Depth {
		analysis.Depth = depth
	}

	entries, err := os.ReadDir(dirPath)
	if err != nil {
		return err
	}

	relPath, _ := filepath.Rel(analysis.RootPath, dirPath)
	if relPath == "." {
		relPath = "root"
	}

	moduleStats := &ModuleStats{
		Path:       dirPath,
		GoFiles:    make([]string, 0),
		TestFiles:  make([]string, 0),
		OtherFiles: make([]string, 0),
		SubDirs:    make([]string, 0),
	}

	for _, entry := range entries {
		name := entry.Name()
		fullPath := filepath.Join(dirPath, name)

		if entry.IsDir() {
			// 跳过隐藏目录和特殊目录
			if strings.HasPrefix(name, ".") || name == "vendor" || name == "node_modules" {
				continue
			}
			moduleStats.SubDirs = append(moduleStats.SubDirs, name)
			// 递归分析子目录
			if err := u.analyzeDirectoryRecursive(fullPath, analysis, depth+1); err != nil {
				log.Printf("警告: 分析子目录 %s 失败: %v", fullPath, err)
			}
		} else {
			// 分析文件
			ext := filepath.Ext(name)
			analysis.FilesByType[ext] = append(analysis.FilesByType[ext], fullPath)
			analysis.TotalFiles++
			moduleStats.FileCount++

			if ext == ".go" {
				if strings.HasSuffix(name, "_test.go") {
					moduleStats.TestFiles = append(moduleStats.TestFiles, name)
				} else {
					moduleStats.GoFiles = append(moduleStats.GoFiles, name)
				}
			} else {
				moduleStats.OtherFiles = append(moduleStats.OtherFiles, name)
			}

			// 计算行数（仅针对Go文件）
			if ext == ".go" {
				if lineCount, err := u.countLines(fullPath); err == nil {
					moduleStats.LineCount += lineCount
				}
			}
		}
	}

	analysis.Modules[relPath] = moduleStats
	return nil
}

// countLines 计算文件行数
func (u *Utils) countLines(filePath string) (int, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return 0, err
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	lines := 0
	for scanner.Scan() {
		lines++
	}

	return lines, scanner.Err()
}

// GenerateDirectoryReport 生成目录结构报告
func (u *Utils) GenerateDirectoryReport(analysis *DirectoryAnalysis) string {
	var report strings.Builder

	report.WriteString("# 目录结构分析报告\n\n")
	report.WriteString(fmt.Sprintf("**根目录**: %s\n", analysis.RootPath))
	report.WriteString(fmt.Sprintf("**总文件数**: %d\n", analysis.TotalFiles))
	report.WriteString(fmt.Sprintf("**目录深度**: %d\n", analysis.Depth))
	report.WriteString(fmt.Sprintf("**模块数量**: %d\n\n", len(analysis.Modules)))

	// 文件类型统计
	report.WriteString("## 文件类型统计\n\n")
	for ext, files := range analysis.FilesByType {
		displayExt := ext
		if ext == "" {
			displayExt = "无扩展名"
		}
		report.WriteString(fmt.Sprintf("- **%s**: %d个文件\n", displayExt, len(files)))
	}
	report.WriteString("\n")

	// 模块统计
	report.WriteString("## 模块统计\n\n")
	for moduleName, stats := range analysis.Modules {
		report.WriteString(fmt.Sprintf("### %s\n\n", moduleName))
		report.WriteString(fmt.Sprintf("- **路径**: `%s`\n", stats.Path))
		report.WriteString(fmt.Sprintf("- **Go文件**: %d个\n", len(stats.GoFiles)))
		report.WriteString(fmt.Sprintf("- **测试文件**: %d个\n", len(stats.TestFiles)))
		report.WriteString(fmt.Sprintf("- **其他文件**: %d个\n", len(stats.OtherFiles)))
		report.WriteString(fmt.Sprintf("- **代码行数**: %d行\n", stats.LineCount))
		report.WriteString(fmt.Sprintf("- **子目录**: %d个\n", len(stats.SubDirs)))

		if len(stats.GoFiles) > 0 {
			report.WriteString("  - Go文件列表: ")
			report.WriteString(strings.Join(stats.GoFiles, ", "))
			report.WriteString("\n")
		}

		if len(stats.SubDirs) > 0 {
			report.WriteString("  - 子目录: ")
			report.WriteString(strings.Join(stats.SubDirs, ", "))
			report.WriteString("\n")
		}

		report.WriteString("\n")
	}

	return report.String()
}
