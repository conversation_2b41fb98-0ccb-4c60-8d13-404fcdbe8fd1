package newapi

import (
	"bytes"
	"fmt"
	"go/ast"
	"go/format"
	"go/token"
	"os"
	"path/filepath"
	"strings"
)

// ASTDefinitionGenerator 基于API定义的代码生成器
type ASTDefinitionGenerator struct {
	APIDefinition *APIDefinition
	OutputDir     string
	PackageName   string
	FileSet       *token.FileSet
}

// NewASTGeneratorFromDefinition 从API定义创建AST代码生成器
func NewASTGeneratorFromDefinition(apiDef *APIDefinition, outputDir, packageName string) *ASTDefinitionGenerator {
	return &ASTDefinitionGenerator{
		APIDefinition: apiDef,
		OutputDir:     outputDir,
		PackageName:   packageName,
		FileSet:       token.NewFileSet(),
	}
}

// Generate 生成所有代码文件
func (g *ASTDefinitionGenerator) Generate() error {
	if err := os.MkdirAll(g.OutputDir, 0755); err != nil {
		return fmt.Errorf("创建输出目录失败: %v", err)
	}

	// 基于API定义生成文件
	if err := g.generateTypesFromDefinition(); err != nil {
		return fmt.Errorf("生成类型文件失败: %v", err)
	}

	if err := g.generateHandlersFromDefinition(); err != nil {
		return fmt.Errorf("生成处理器文件失败: %v", err)
	}

	if err := g.generateRoutesFromDefinition(); err != nil {
		return fmt.Errorf("生成路由文件失败: %v", err)
	}

	if err := g.generateMainFromDefinition(); err != nil {
		return fmt.Errorf("生成主文件失败: %v", err)
	}

	return nil
}

// generateTypesFromDefinition 基于API定义生成类型文件
func (g *ASTDefinitionGenerator) generateTypesFromDefinition() error {
	file := &ast.File{
		Name: ast.NewIdent(g.PackageName),
		Decls: []ast.Decl{
			&ast.GenDecl{
				Tok: token.IMPORT,
				Specs: []ast.Spec{
					&ast.ImportSpec{Path: &ast.BasicLit{Kind: token.STRING, Value: `"encoding/json"`}},
					&ast.ImportSpec{Path: &ast.BasicLit{Kind: token.STRING, Value: `"time"`}},
				},
			},
		},
	}

	// 从API定义中生成类型
	for _, typeDef := range g.APIDefinition.Types {
		if typeDef.IsStruct {
			typeDecl := g.createStructDeclFromDefinition(typeDef)
			file.Decls = append(file.Decls, typeDecl)
		}
	}

	return g.writeASTToFileExt(file, "types.go")
}

// generateHandlersFromDefinition 基于API定义生成处理器文件
func (g *ASTDefinitionGenerator) generateHandlersFromDefinition() error {
	file := &ast.File{
		Name: ast.NewIdent(g.PackageName),
		Decls: []ast.Decl{
			&ast.GenDecl{
				Tok: token.IMPORT,
				Specs: []ast.Spec{
					&ast.ImportSpec{Path: &ast.BasicLit{Kind: token.STRING, Value: `"net/http"`}},
					&ast.ImportSpec{Path: &ast.BasicLit{Kind: token.STRING, Value: `"github.com/gin-gonic/gin"`}},
				},
			},
		},
	}

	// 收集所有处理器
	handlerMap := make(map[string]*EndpointDefinition)

	// 从路由组收集处理器
	for _, group := range g.APIDefinition.Groups {
		for _, endpoint := range group.Endpoints {
			if endpoint.Handler != "" {
				handlerMap[endpoint.Handler] = endpoint
			}
		}
	}

	// 从独立端点收集处理器
	for _, endpoint := range g.APIDefinition.Endpoints {
		if endpoint.Handler != "" {
			handlerMap[endpoint.Handler] = endpoint
		}
	}

	// 生成处理器函数
	for _, endpoint := range handlerMap {
		handlerDecl := g.createHandlerFuncFromDefinition(endpoint)
		file.Decls = append(file.Decls, handlerDecl)
	}

	return g.writeASTToFileExt(file, "handlers.go")
}

// generateRoutesFromDefinition 基于API定义生成路由文件
func (g *ASTDefinitionGenerator) generateRoutesFromDefinition() error {
	file := &ast.File{
		Name: ast.NewIdent(g.PackageName),
		Decls: []ast.Decl{
			&ast.GenDecl{
				Tok: token.IMPORT,
				Specs: []ast.Spec{
					&ast.ImportSpec{Path: &ast.BasicLit{Kind: token.STRING, Value: `"github.com/gin-gonic/gin"`}},
				},
			},
		},
	}

	// 生成SetupRoutes函数
	setupRoutesFunc := g.createSetupRoutesFuncFromDefinition()
	file.Decls = append(file.Decls, setupRoutesFunc)

	return g.writeASTToFileExt(file, "routes.go")
}

// generateMainFromDefinition 基于API定义生成主文件
func (g *ASTDefinitionGenerator) generateMainFromDefinition() error {
	file := &ast.File{
		Name: ast.NewIdent("main"),
		Decls: []ast.Decl{
			&ast.GenDecl{
				Tok: token.IMPORT,
				Specs: []ast.Spec{
					&ast.ImportSpec{Path: &ast.BasicLit{Kind: token.STRING, Value: `"log"`}},
					&ast.ImportSpec{Path: &ast.BasicLit{Kind: token.STRING, Value: `"github.com/gin-gonic/gin"`}},
				},
			},
		},
	}

	// 生成main函数
	mainFunc := g.createMainFuncFromDefinition()
	file.Decls = append(file.Decls, mainFunc)

	return g.writeASTToFileExt(file, "main.go")
}

// 辅助方法实现
func (g *ASTDefinitionGenerator) createStructDeclFromDefinition(typeDef *TypeDefinition) *ast.GenDecl {
	fields := make([]*ast.Field, 0)

	for _, fieldDef := range typeDef.Fields {
		field := &ast.Field{
			Names: []*ast.Ident{ast.NewIdent(fieldDef.Name)},
			Type:  g.parseTypeStringFromDefinition(fieldDef.Type),
		}

		// 生成标签
		tagParts := make([]string, 0)
		if fieldDef.JSONTag != "" {
			tagParts = append(tagParts, fmt.Sprintf(`json:"%s"`, fieldDef.JSONTag))
		}
		if fieldDef.FormTag != "" {
			tagParts = append(tagParts, fmt.Sprintf(`form:"%s"`, fieldDef.FormTag))
		}
		if fieldDef.ValidTag != "" {
			tagParts = append(tagParts, fmt.Sprintf(`validate:"%s"`, fieldDef.ValidTag))
		}

		if len(tagParts) > 0 {
			field.Tag = &ast.BasicLit{
				Kind:  token.STRING,
				Value: fmt.Sprintf("`%s`", strings.Join(tagParts, " ")),
			}
		}

		fields = append(fields, field)
	}

	structType := &ast.StructType{
		Fields: &ast.FieldList{List: fields},
	}

	typeSpec := &ast.TypeSpec{
		Name: ast.NewIdent(typeDef.Name),
		Type: structType,
	}

	genDecl := &ast.GenDecl{
		Tok:   token.TYPE,
		Specs: []ast.Spec{typeSpec},
	}

	// 添加注释
	if typeDef.Comment != "" {
		genDecl.Doc = &ast.CommentGroup{
			List: []*ast.Comment{
				{Text: fmt.Sprintf("// %s %s", typeDef.Name, strings.TrimSpace(typeDef.Comment))},
			},
		}
	}

	return genDecl
}

func (g *ASTDefinitionGenerator) createHandlerFuncFromDefinition(endpoint *EndpointDefinition) *ast.FuncDecl {
	stmts := []ast.Stmt{}

	// 如果有请求类型，添加绑定逻辑
	if endpoint.RequestType != nil {
		// var req RequestType
		reqDecl := &ast.DeclStmt{
			Decl: &ast.GenDecl{
				Tok: token.VAR,
				Specs: []ast.Spec{
					&ast.ValueSpec{
						Names: []*ast.Ident{ast.NewIdent("req")},
						Type:  ast.NewIdent(endpoint.RequestType.Name),
					},
				},
			},
		}
		stmts = append(stmts, reqDecl)
	}

	// 添加简单的成功响应
	responseStmt := &ast.ExprStmt{
		X: &ast.CallExpr{
			Fun: &ast.SelectorExpr{
				X:   ast.NewIdent("c"),
				Sel: ast.NewIdent("JSON"),
			},
			Args: []ast.Expr{
				&ast.SelectorExpr{X: ast.NewIdent("http"), Sel: ast.NewIdent("StatusOK")},
				&ast.CompositeLit{
					Type: &ast.SelectorExpr{X: ast.NewIdent("gin"), Sel: ast.NewIdent("H")},
					Elts: []ast.Expr{
						&ast.KeyValueExpr{
							Key:   &ast.BasicLit{Kind: token.STRING, Value: `"code"`},
							Value: &ast.BasicLit{Kind: token.INT, Value: "200"},
						},
						&ast.KeyValueExpr{
							Key:   &ast.BasicLit{Kind: token.STRING, Value: `"message"`},
							Value: &ast.BasicLit{Kind: token.STRING, Value: `"success"`},
						},
					},
				},
			},
		},
	}
	stmts = append(stmts, responseStmt)

	// 创建函数声明
	funcDecl := &ast.FuncDecl{
		Name: ast.NewIdent(endpoint.Handler),
		Type: &ast.FuncType{
			Params: &ast.FieldList{
				List: []*ast.Field{
					{
						Names: []*ast.Ident{ast.NewIdent("c")},
						Type: &ast.StarExpr{
							X: &ast.SelectorExpr{X: ast.NewIdent("gin"), Sel: ast.NewIdent("Context")},
						},
					},
				},
			},
		},
		Body: &ast.BlockStmt{List: stmts},
	}

	// 添加注释
	if endpoint.Summary != "" {
		funcDecl.Doc = &ast.CommentGroup{
			List: []*ast.Comment{
				{Text: fmt.Sprintf("// %s %s", endpoint.Handler, endpoint.Summary)},
			},
		}
	}

	return funcDecl
}

func (g *ASTDefinitionGenerator) createSetupRoutesFuncFromDefinition() *ast.FuncDecl {
	stmts := []ast.Stmt{}

	// 为每个路由组生成代码
	for _, group := range g.APIDefinition.Groups {
		if len(group.Endpoints) == 0 {
			continue
		}

		// 创建路由组变量
		groupName := g.sanitizeVarNameFromDefinition(group.Prefix)
		groupDecl := &ast.AssignStmt{
			Lhs: []ast.Expr{ast.NewIdent(groupName)},
			Tok: token.DEFINE,
			Rhs: []ast.Expr{
				&ast.CallExpr{
					Fun:  &ast.SelectorExpr{X: ast.NewIdent("r"), Sel: ast.NewIdent("Group")},
					Args: []ast.Expr{&ast.BasicLit{Kind: token.STRING, Value: fmt.Sprintf(`"%s"`, group.Prefix)}},
				},
			},
		}
		stmts = append(stmts, groupDecl)

		// 创建路由组块
		blockStmts := []ast.Stmt{}
		for _, endpoint := range group.Endpoints {
			routeStmt := &ast.ExprStmt{
				X: &ast.CallExpr{
					Fun: &ast.SelectorExpr{X: ast.NewIdent(groupName), Sel: ast.NewIdent(endpoint.Method)},
					Args: []ast.Expr{
						&ast.BasicLit{Kind: token.STRING, Value: fmt.Sprintf(`"%s"`, endpoint.Path)},
						ast.NewIdent(endpoint.Handler),
					},
				},
			}
			blockStmts = append(blockStmts, routeStmt)
		}
		stmts = append(stmts, &ast.BlockStmt{List: blockStmts})
	}

	// 添加独立端点
	for _, endpoint := range g.APIDefinition.Endpoints {
		routeStmt := &ast.ExprStmt{
			X: &ast.CallExpr{
				Fun: &ast.SelectorExpr{X: ast.NewIdent("r"), Sel: ast.NewIdent(endpoint.Method)},
				Args: []ast.Expr{
					&ast.BasicLit{Kind: token.STRING, Value: fmt.Sprintf(`"%s"`, endpoint.Path)},
					ast.NewIdent(endpoint.Handler),
				},
			},
		}
		stmts = append(stmts, routeStmt)
	}

	return &ast.FuncDecl{
		Name: ast.NewIdent("SetupRoutes"),
		Type: &ast.FuncType{
			Params: &ast.FieldList{
				List: []*ast.Field{
					{
						Names: []*ast.Ident{ast.NewIdent("r")},
						Type:  &ast.StarExpr{X: &ast.SelectorExpr{X: ast.NewIdent("gin"), Sel: ast.NewIdent("Engine")}},
					},
				},
			},
		},
		Body: &ast.BlockStmt{List: stmts},
		Doc:  &ast.CommentGroup{List: []*ast.Comment{{Text: "// SetupRoutes 设置路由"}}},
	}
}

func (g *ASTDefinitionGenerator) createMainFuncFromDefinition() *ast.FuncDecl {
	comments := []*ast.Comment{{Text: "// main 主函数"}}

	if g.APIDefinition.Info != nil {
		comments = append(comments, &ast.Comment{
			Text: fmt.Sprintf("// %s v%s", g.APIDefinition.Info.Title, g.APIDefinition.Info.Version),
		})
	}

	return &ast.FuncDecl{
		Name: ast.NewIdent("main"),
		Type: &ast.FuncType{},
		Body: &ast.BlockStmt{
			List: []ast.Stmt{
				&ast.AssignStmt{
					Lhs: []ast.Expr{ast.NewIdent("r")},
					Tok: token.DEFINE,
					Rhs: []ast.Expr{&ast.CallExpr{Fun: &ast.SelectorExpr{X: ast.NewIdent("gin"), Sel: ast.NewIdent("Default")}}},
				},
				&ast.ExprStmt{X: &ast.CallExpr{Fun: ast.NewIdent("SetupRoutes"), Args: []ast.Expr{ast.NewIdent("r")}}},
				&ast.ExprStmt{X: &ast.CallExpr{
					Fun:  &ast.SelectorExpr{X: ast.NewIdent("log"), Sel: ast.NewIdent("Println")},
					Args: []ast.Expr{&ast.BasicLit{Kind: token.STRING, Value: `"服务器启动在 :8080"`}},
				}},
				&ast.ExprStmt{X: &ast.CallExpr{
					Fun:  &ast.SelectorExpr{X: ast.NewIdent("r"), Sel: ast.NewIdent("Run")},
					Args: []ast.Expr{&ast.BasicLit{Kind: token.STRING, Value: `":8080"`}},
				}},
			},
		},
		Doc: &ast.CommentGroup{List: comments},
	}
}

func (g *ASTDefinitionGenerator) parseTypeStringFromDefinition(typeStr string) ast.Expr {
	switch {
	case strings.HasPrefix(typeStr, "[]"):
		return &ast.ArrayType{Elt: g.parseTypeStringFromDefinition(strings.TrimPrefix(typeStr, "[]"))}
	case strings.HasPrefix(typeStr, "*"):
		return &ast.StarExpr{X: g.parseTypeStringFromDefinition(strings.TrimPrefix(typeStr, "*"))}
	case strings.Contains(typeStr, "."):
		parts := strings.Split(typeStr, ".")
		return &ast.SelectorExpr{X: ast.NewIdent(parts[0]), Sel: ast.NewIdent(parts[1])}
	default:
		return ast.NewIdent(typeStr)
	}
}

func (g *ASTDefinitionGenerator) sanitizeVarNameFromDefinition(name string) string {
	name = strings.TrimPrefix(name, "/")
	name = strings.ReplaceAll(name, "/", "_")
	name = strings.ReplaceAll(name, "-", "_")
	if name == "" {
		name = "group"
	}
	return name + "Group"
}

func (g *ASTDefinitionGenerator) writeASTToFileExt(file *ast.File, filename string) error {
	// 直接生成并写入AST文件
	return g.writeASTToFile(file, filename)
}

// writeASTToFile 将AST文件写入磁盘
func (g *ASTDefinitionGenerator) writeASTToFile(file *ast.File, filename string) error {
	// 创建输出缓冲区
	var buf bytes.Buffer

	// 使用go/format包将AST转换为格式化的代码
	err := format.Node(&buf, g.FileSet, file)
	if err != nil {
		return fmt.Errorf("格式化AST失败: %v", err)
	}

	// 构建完整的文件路径
	filePath := filepath.Join(g.OutputDir, filename)

	// 写入文件
	err = os.WriteFile(filePath, buf.Bytes(), 0644)
	if err != nil {
		return fmt.Errorf("写入文件 %s 失败: %v", filePath, err)
	}

	return nil
}
