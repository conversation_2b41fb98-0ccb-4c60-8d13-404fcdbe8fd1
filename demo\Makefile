# Makefile for NewAPI demo and testing

# 默认目标
.PHONY: all
all: run

# 运行演示程序
.PHONY: run
run:
	go run .

# 运行测试
.PHONY: test
test:
	go test -v ./...

# 运行特定测试
.PHONY: test-generator
test-generator:
	go test -v ./generator_test.go

# 清理临时文件
.PHONY: clean
clean:
	rm -rf test_output/

# 格式化代码
.PHONY: fmt
fmt:
	go fmt ./...

# 检查代码
.PHONY: vet
vet:
	go vet ./...

# 安装依赖
.PHONY: install
install:
	go mod tidy

# 帮助信息
.PHONY: help
help:
	@echo "NewAPI Demo Makefile"
	@echo "===================="
	@echo "run        - 运行演示程序"
	@echo "test       - 运行所有测试"
	@echo "test-generator - 运行生成器测试"
	@echo "clean      - 清理临时文件"
	@echo "fmt        - 格式化代码"
	@echo "vet        - 检查代码"
	@echo "install    - 安装依赖"
	@echo "help       - 显示帮助信息"