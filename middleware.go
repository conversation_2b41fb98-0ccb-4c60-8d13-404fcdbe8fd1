package newapi

import (
	"net/http"
)

// MiddlewareRegistry 中间件注册表
type MiddlewareRegistry struct {
	middlewares map[string]MiddlewareFunc
}

// NewMiddlewareRegistry 创建新的中间件注册表
func NewMiddlewareRegistry() *MiddlewareRegistry {
	return &MiddlewareRegistry{
		middlewares: make(map[string]MiddlewareFunc),
	}
}

// Register 注册中间件
func (mr *MiddlewareRegistry) Register(name string, middleware MiddlewareFunc) {
	mr.middlewares[name] = middleware
}

// Get 获取中间件
func (mr *MiddlewareRegistry) Get(name string) (MiddlewareFunc, bool) {
	middleware, exists := mr.middlewares[name]
	return middleware, exists
}

// GetAll 获取所有中间件
func (mr *MiddlewareRegistry) GetAll() map[string]MiddlewareFunc {
	return mr.middlewares
}

// 预定义的中间件类型

// AuthMiddleware 认证中间件配置
type AuthMiddleware struct {
	TokenHeader  string   `json:"token_header"`
	TokenQuery   string   `json:"token_query"`
	SkipPaths    []string `json:"skip_paths"`
	RequiredRole []string `json:"required_role"`
}

// CORSMiddleware CORS中间件配置
type CORSMiddleware struct {
	AllowOrigins     []string `json:"allow_origins"`
	AllowMethods     []string `json:"allow_methods"`
	AllowHeaders     []string `json:"allow_headers"`
	ExposeHeaders    []string `json:"expose_headers"`
	AllowCredentials bool     `json:"allow_credentials"`
	MaxAge           int      `json:"max_age"`
}

// RateLimitMiddleware 限流中间件配置
type RateLimitMiddleware struct {
	Rate      int      `json:"rate"`       // 每秒允许的请求数
	Burst     int      `json:"burst"`      // 突发请求数
	Window    int      `json:"window"`     // 时间窗口（秒）
	KeyFunc   string   `json:"key_func"`   // 限流键生成函数
	SkipPaths []string `json:"skip_paths"` // 跳过限流的路径
}

// LoggingMiddleware 日志中间件配置
type LoggingMiddleware struct {
	LogLevel    string   `json:"log_level"`
	LogFormat   string   `json:"log_format"`
	SkipPaths   []string `json:"skip_paths"`
	IncludeBody bool     `json:"include_body"`
}

// CacheMiddleware 缓存中间件配置
type CacheMiddleware struct {
	TTL       int      `json:"ttl"`        // 缓存时间（秒）
	KeyPrefix string   `json:"key_prefix"` // 缓存键前缀
	Headers   []string `json:"headers"`    // 需要缓存的响应头
	Methods   []string `json:"methods"`    // 支持缓存的HTTP方法
}

// ValidateMiddleware 参数验证中间件配置
type ValidateMiddleware struct {
	ValidateQuery  bool `json:"validate_query"`
	ValidateBody   bool `json:"validate_body"`
	ValidateParams bool `json:"validate_params"`
	FailFast       bool `json:"fail_fast"`
}

// MetricsMiddleware 监控指标中间件配置
type MetricsMiddleware struct {
	Namespace string   `json:"namespace"`
	Subsystem string   `json:"subsystem"`
	SkipPaths []string `json:"skip_paths"`
}

// SecurityMiddleware 安全中间件配置
type SecurityMiddleware struct {
	XSSProtection         bool     `json:"xss_protection"`
	ContentTypeNoSniff    bool     `json:"content_type_no_sniff"`
	FrameOptions          string   `json:"frame_options"`
	ContentSecurityPolicy string   `json:"content_security_policy"`
	HSTSMaxAge            int      `json:"hsts_max_age"`
	TrustedProxies        []string `json:"trusted_proxies"`
}

// MiddlewareBuilder 中间件构建器
type MiddlewareBuilder struct {
	registry *MiddlewareRegistry
}

// NewMiddlewareBuilder 创建中间件构建器
func NewMiddlewareBuilder() *MiddlewareBuilder {
	return &MiddlewareBuilder{
		registry: NewMiddlewareRegistry(),
	}
}

// Auth 添加认证中间件
func (mb *MiddlewareBuilder) Auth(config AuthMiddleware) *MiddlewareBuilder {
	mb.registry.Register("auth", func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 实际的认证逻辑会在生成的代码中实现
			next.ServeHTTP(w, r)
		}
	})
	return mb
}

// CORS 添加CORS中间件
func (mb *MiddlewareBuilder) CORS(config CORSMiddleware) *MiddlewareBuilder {
	mb.registry.Register("cors", func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 实际的CORS逻辑会在生成的代码中实现
			next.ServeHTTP(w, r)
		}
	})
	return mb
}

// RateLimit 添加限流中间件
func (mb *MiddlewareBuilder) RateLimit(config RateLimitMiddleware) *MiddlewareBuilder {
	mb.registry.Register("ratelimit", func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 实际的限流逻辑会在生成的代码中实现
			next.ServeHTTP(w, r)
		}
	})
	return mb
}

// Logging 添加日志中间件
func (mb *MiddlewareBuilder) Logging(config LoggingMiddleware) *MiddlewareBuilder {
	mb.registry.Register("logging", func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 实际的日志逻辑会在生成的代码中实现
			next.ServeHTTP(w, r)
		}
	})
	return mb
}

// Cache 添加缓存中间件
func (mb *MiddlewareBuilder) Cache(config CacheMiddleware) *MiddlewareBuilder {
	mb.registry.Register("cache", func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 实际的缓存逻辑会在生成的代码中实现
			next.ServeHTTP(w, r)
		}
	})
	return mb
}

// Validate 添加验证中间件
func (mb *MiddlewareBuilder) Validate(config ValidateMiddleware) *MiddlewareBuilder {
	mb.registry.Register("validate", func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 实际的验证逻辑会在生成的代码中实现
			next.ServeHTTP(w, r)
		}
	})
	return mb
}

// Metrics 添加监控中间件
func (mb *MiddlewareBuilder) Metrics(config MetricsMiddleware) *MiddlewareBuilder {
	mb.registry.Register("metrics", func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 实际的监控逻辑会在生成的代码中实现
			next.ServeHTTP(w, r)
		}
	})
	return mb
}

// Security 添加安全中间件
func (mb *MiddlewareBuilder) Security(config SecurityMiddleware) *MiddlewareBuilder {
	mb.registry.Register("security", func(next http.HandlerFunc) http.HandlerFunc {
		return func(w http.ResponseWriter, r *http.Request) {
			// 实际的安全逻辑会在生成的代码中实现
			next.ServeHTTP(w, r)
		}
	})
	return mb
}

// Custom 添加自定义中间件
func (mb *MiddlewareBuilder) Custom(name string, middleware MiddlewareFunc) *MiddlewareBuilder {
	mb.registry.Register(name, middleware)
	return mb
}

// Build 构建中间件注册表
func (mb *MiddlewareBuilder) Build() *MiddlewareRegistry {
	return mb.registry
}

// MiddlewareChain 中间件链
type MiddlewareChain struct {
	middlewares []MiddlewareFunc
}

// NewMiddlewareChain 创建中间件链
func NewMiddlewareChain(middlewares ...MiddlewareFunc) *MiddlewareChain {
	return &MiddlewareChain{
		middlewares: middlewares,
	}
}

// Add 添加中间件
func (mc *MiddlewareChain) Add(middleware MiddlewareFunc) *MiddlewareChain {
	mc.middlewares = append(mc.middlewares, middleware)
	return mc
}

// Execute 执行中间件链
func (mc *MiddlewareChain) Execute(handler http.HandlerFunc) http.HandlerFunc {
	for i := len(mc.middlewares) - 1; i >= 0; i-- {
		handler = mc.middlewares[i](handler)
	}
	return handler
}
