# NewAPI 测试生成演示

这个目录包含了NewAPI的测试生成演示代码。

## 文件说明

- [main.go](file:///d:/app/newapi/demo/main.go): 主演示程序
- [api_example.go](file:///d:/app/newapi/demo/api_example.go): 示例API定义
- [generator_test.go](file:///d:/app/newapi/demo/generator_test.go): 测试生成代码
- [Makefile](file:///d:/app/newapi/demo/Makefile): 构建和测试脚本

## 运行演示

### 使用Go命令运行

```bash
# 运行演示程序
go run .

# 运行测试
go test -v ./...
```

### 使用Makefile运行

```bash
# 运行演示程序
make run

# 运行测试
make test

# 运行特定测试
make test-generator

# 清理临时文件
make clean

# 格式化代码
make fmt

# 检查代码
make vet
```

## 测试说明

测试文件 [generator_test.go](file:///d:/app/newapi/demo/generator_test.go) 包含了以下几个测试用例：

1. **TestAPIGeneration**: 测试API代码生成功能
2. **TestDirectoryAnalysis**: 测试目录结构分析功能
3. **TestModuleDefinition**: 测试模块定义功能
4. **TestAPIDocumentationGeneration**: 测试API文档生成功能

这些测试展示了NewAPI的核心功能，包括：
- API定义和分析
- 目录结构分析
- 代码生成
- 文档生成

## 输出说明

运行演示程序后，你将看到：
1. 当前项目的目录结构分析
2. API定义的分析结果
3. 项目统计信息
4. 模块层次结构
5. 测试运行说明