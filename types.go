package newapi

import (
	"fmt"
	"strings"
	"time"
)

// 通用请求类型

// CommonResp 通用响应结构
type CommonResp struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// SuccessResp 成功响应
type SuccessResp struct {
	Message string `json:"message"`
}

// ErrorResp 错误响应
type ErrorResp struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	Details string `json:"details,omitempty"`
}

// PageReq 分页请求
type PageReq struct {
	Page      int    `form:"page,default=1"`
	PageSize  int    `form:"page_size,default=10"`
	Keyword   string `form:"keyword"`
	SortBy    string `form:"sort_by"`
	SortOrder string `form:"sort_order,options=asc|desc"`
}

// PageResp 分页响应
type PageResp struct {
	Total      int64       `json:"total"`
	Page       int         `json:"page"`
	PageSize   int         `json:"page_size"`
	TotalPages int         `json:"total_pages"`
	HasNext    bool        `json:"has_next"`
	HasPrev    bool        `json:"has_prev"`
	Data       interface{} `json:"data"`
}

// IdReq ID请求参数
type IdReq struct {
	Id int64 `uri:"id" validate:"required,min=1"`
}

// IdPathReq 路径中的ID参数
type IdPathReq struct {
	Id int64 `path:"id" validate:"required,min=1"`
}

// BatchIdReq 批量ID请求
type BatchIdReq struct {
	Ids []int64 `json:"ids" validate:"required,min=1"`
}

// StatusReq 状态更新请求
type StatusReq struct {
	Id     int64 `uri:"id" validate:"required,min=1"`
	Status int   `json:"status" validate:"required,oneof=0 1"`
}

// BatchOperationResp 批量操作响应
type BatchOperationResp struct {
	SuccessCount int     `json:"success_count"`
	FailedCount  int     `json:"failed_count"`
	FailedIds    []int64 `json:"failed_ids,omitempty"`
	Message      string  `json:"message"`
}

// UploadResp 文件上传响应
type UploadResp struct {
	Url      string `json:"url"`
	Filename string `json:"filename"`
	Size     int64  `json:"size"`
	MimeType string `json:"mime_type"`
}

// Option 选项类型
type Option struct {
	Label    string      `json:"label"`
	Value    interface{} `json:"value"`
	Disabled bool        `json:"disabled,omitempty"`
}

// TimeRange 时间范围
type TimeRange struct {
	StartTime *time.Time `json:"start_time,omitempty"`
	EndTime   *time.Time `json:"end_time,omitempty"`
}

// SearchReq 搜索请求
type SearchReq struct {
	Query     string            `form:"query"`
	Filters   map[string]string `form:"filters"`
	TimeRange *TimeRange        `form:"time_range"`
	PageReq
}

// 字段验证标签定义

// ValidationTags 定义常用的验证标签
type ValidationTags struct{}

// Required 必填字段
func (ValidationTags) Required() string {
	return "required"
}

// Email 邮箱格式
func (ValidationTags) Email() string {
	return "email"
}

// Phone 手机号格式
func (ValidationTags) Phone() string {
	return "phone"
}

// URL URL格式
func (ValidationTags) URL() string {
	return "url"
}

// MinLength 最小长度
func (ValidationTags) MinLength(length int) string {
	return fmt.Sprintf("min=%d", length)
}

// MaxLength 最大长度
func (ValidationTags) MaxLength(length int) string {
	return fmt.Sprintf("max=%d", length)
}

// OneOf 枚举值
func (ValidationTags) OneOf(values ...string) string {
	return fmt.Sprintf("oneof=%s", strings.Join(values, " "))
}

// Range 数值范围
func (ValidationTags) Range(min, max int) string {
	return fmt.Sprintf("min=%d,max=%d", min, max)
}

// 全局验证实例
var Validate = ValidationTags{}

// 定义常用的JSON标签

// JSONTags JSON标签工具
type JSONTags struct{}

// Optional 可选字段
func (JSONTags) Optional() string {
	return ",omitempty"
}

// Ignore 忽略字段
func (JSONTags) Ignore() string {
	return "-"
}

// 全局JSON标签实例
var JSON = JSONTags{}

// ModuleDefinition 功能模块定义
type ModuleDefinition struct {
	Name        string                `json:"name"`        // 模块名称
	Path        string                `json:"path"`        // 模块路径
	Description string                `json:"description"` // 模块描述
	Package     string                `json:"package"`     // 包名
	Types       []*TypeDefinition     `json:"types"`       // 模块中的类型定义
	Groups      []*GroupDefinition    `json:"groups"`      // 模块中的路由组
	Endpoints   []*EndpointDefinition `json:"endpoints"`   // 模块中的端点
	SubModules  []*ModuleDefinition   `json:"sub_modules"` // 子模块
	Files       []string              `json:"files"`       // 模块包含的文件
	DependsOn   []string              `json:"depends_on"`  // 依赖的其他模块
}

// FileInfo 文件信息
type FileInfo struct {
	Path         string `json:"path"`          // 文件路径
	RelativePath string `json:"relative_path"` // 相对路径
	Directory    string `json:"directory"`     // 所在目录
	Module       string `json:"module"`        // 所属模块
	Package      string `json:"package"`       // 包名
	Size         int64  `json:"size"`          // 文件大小
}
