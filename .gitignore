# Go相关文件
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.mod
go.sum

# IDE相关文件
.vscode/
.idea/
*.swp
*.swo
*~

# 数据库文件
data/*.db
data/*.db-*
*.db

# 日志文件
*.log

# 编译生成的文件
server/*.zero.go
server/internal/handler/*/*.zero.go
server/internal/logic/*/*.zero.go
server/internal/svc/*.zero.go
server/internal/types/*.zero.go
server/internal/config/*.zero.go

# backend 生成代码（保留 svc 和 logic 目录）
backend/handler/
backend/routes/
backend/types/
backend/config/
backend/middleware/
backend/internal/
backend/admin_service.go
backend/etc/

# 环境配置文件
server/etc/*.yaml
server/etc/*.json

# 临时文件
tmp/
temp/

# 操作系统相关
.DS_Store
Thumbs.db

# coverage report
coverage.txt
coverage.html

# vim相关
*.swp
*.swo

# Emacs相关
*~
\#*\#

# 自定义忽略项
.env
config.json