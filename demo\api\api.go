package api

import (
	"admin-amis-zero/newapi"
)

// User 用户信息
type User struct {
	ID       int64  `json:"id" validate:"required"`
	Username string `json:"username" validate:"required,min=3,max=20"`
	Email    string `json:"email" validate:"required,email"`
	Phone    string `json:"phone,omitempty" validate:"phone"`
	Status   int    `json:"status" validate:"oneof=0 1"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" validate:"required,min=3,max=20"`
	Email    string `json:"email" validate:"required,email"`
	Phone    string `json:"phone,omitempty" validate:"phone"`
	Password string `json:"password" validate:"required,min=6"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Username string `json:"username,omitempty" validate:"min=3,max=20"`
	Email    string `json:"email,omitempty" validate:"email"`
	Phone    string `json:"phone,omitempty" validate:"phone"`
	Status   int    `json:"status,omitempty" validate:"oneof=0 1"`
}

// UserAPI 用户管理API
func UserAPI(api *newapi.API) {
	// 创建用户组
	userGroup := newapi.NewGroup("/users").
		WithTags("用户管理")

	// 获取用户列表
	userGroup.GET("/").
		Handler("ListUsers").
		Summary("获取用户列表").
		Description("分页获取用户列表信息").
		Request(&newapi.PageReq{}).
		Response(&newapi.CommonResp{Data: &newapi.PageResp{Data: []User{}}}).
		Build()

	// 获取用户详情
	userGroup.GET("/:id").
		Handler("GetUser").
		Summary("获取用户详情").
		Description("根据用户ID获取用户详细信息").
		Request(&newapi.IdReq{}).
		Response(&newapi.CommonResp{Data: &User{}}).
		Build()

	// 创建用户
	userGroup.POST("/").
		Handler("CreateUser").
		Summary("创建用户").
		Description("创建一个新的用户").
		Request(&CreateUserRequest{}).
		Response(&newapi.CommonResp{Data: &User{}}).
		Build()

	// 更新用户
	userGroup.PUT("/:id").
		Handler("UpdateUser").
		Summary("更新用户").
		Description("根据用户ID更新用户信息").
		Request(&UpdateUserRequest{}).
		Response(&newapi.CommonResp{Data: &User{}}).
		Build()

	// 删除用户
	userGroup.DELETE("/:id").
		Handler("DeleteUser").
		Summary("删除用户").
		Description("根据用户ID删除用户").
		Request(&newapi.IdReq{}).
		Response(&newapi.SuccessResp{}).
		Build()

	api.AddGroup(*userGroup)
}

// ProductAPI 产品管理API
func ProductAPI(api *newapi.API) {
	// 创建产品组
	productGroup := newapi.NewGroup("/products").
		WithTags("产品管理")

	// 获取产品列表
	productGroup.GET("/").
		Handler("ListProducts").
		Summary("获取产品列表").
		Description("分页获取产品列表信息").
		Request(&newapi.PageReq{}).
		Response(&newapi.CommonResp{Data: &newapi.PageResp{Data: []Product{}}}).
		Build()

	// 获取产品详情
	productGroup.GET("/:id").
		Handler("GetProduct").
		Summary("获取产品详情").
		Description("根据产品ID获取产品详细信息").
		Request(&newapi.IdReq{}).
		Response(&newapi.CommonResp{Data: &Product{}}).
		Build()

	api.AddGroup(*productGroup)
}

// Product 产品信息
type Product struct {
	ID          int64  `json:"id" validate:"required"`
	Name        string `json:"name" validate:"required,min=1,max=100"`
	Description string `json:"description,omitempty"`
	Price       int64  `json:"price" validate:"required,min=0"`
	Status      int    `json:"status" validate:"oneof=0 1"`
}
