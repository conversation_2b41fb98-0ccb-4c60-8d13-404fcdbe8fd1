package user

// User 用户信息
type User struct {
	ID       int64  `json:"id" validate:"required"`
	Username string `json:"username" validate:"required,min=3,max=20"`
	Email    string `json:"email" validate:"required,email"`
	Phone    string `json:"phone,omitempty" validate:"phone"`
	Status   int    `json:"status" validate:"oneof=0 1"`
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" validate:"required,min=3,max=20"`
	Email    string `json:"email" validate:"required,email"`
	Phone    string `json:"phone,omitempty" validate:"phone"`
	Password string `json:"password" validate:"required,min=6"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Username string `json:"username,omitempty" validate:"min=3,max=20"`
	Email    string `json:"email,omitempty" validate:"email"`
	Phone    string `json:"phone,omitempty" validate:"phone"`
	Status   int    `json:"status,omitempty" validate:"oneof=0 1"`
}
